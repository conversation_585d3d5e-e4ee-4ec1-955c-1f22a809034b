import { AuthService } from '../services/authService';
import { SecureTokenStorage } from './tokenStorage';

export interface AuthTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

export class AuthTestUtils {
  /**
   * Test complete authentication flow
   */
  static async testAuthFlow(): Promise<AuthTestResult[]> {
    const results: AuthTestResult[] = [];

    try {
      // Test 1: Login
      console.log('Testing login...');
      const loginResult = await AuthService.login({
        username: 'admin',
        password: 'admin123'
      });
      
      results.push({
        success: true,
        message: 'Login successful',
        data: { hasTokens: !!(loginResult.access_token && loginResult.refresh_token) }
      });

      // Test 2: Get user profile
      console.log('Testing user profile fetch...');
      const user = await AuthService.getCurrentUser();
      
      results.push({
        success: true,
        message: 'User profile fetch successful',
        data: { userId: user.id, username: user.username }
      });

      // Test 3: Token refresh
      console.log('Testing token refresh...');
      const refreshResult = await AuthService.refreshToken(loginResult.refresh_token);
      
      results.push({
        success: true,
        message: 'Token refresh successful',
        data: { hasNewToken: !!refreshResult.access_token }
      });

      // Test 4: Token storage
      console.log('Testing secure token storage...');
      SecureTokenStorage.setTokens(refreshResult.access_token, refreshResult.refresh_token);
      const storedToken = SecureTokenStorage.getAccessToken();
      
      results.push({
        success: !!storedToken,
        message: storedToken ? 'Token storage successful' : 'Token storage failed',
        data: { tokenStored: !!storedToken }
      });

    } catch (error) {
      results.push({
        success: false,
        message: 'Authentication test failed',
        error: error
      });
    }

    return results;
  }

  /**
   * Test token validation
   */
  static testTokenValidation(): AuthTestResult {
    try {
      const token = SecureTokenStorage.getAccessToken();
      
      if (!token) {
        return {
          success: false,
          message: 'No token found'
        };
      }

      const isValid = SecureTokenStorage.isValidTokenFormat(token);
      const isExpired = SecureTokenStorage.isTokenExpired(token);
      const expiration = SecureTokenStorage.getTokenExpiration(token);

      return {
        success: isValid && !isExpired,
        message: `Token validation: ${isValid ? 'valid format' : 'invalid format'}, ${isExpired ? 'expired' : 'not expired'}`,
        data: {
          isValid,
          isExpired,
          expiration: expiration?.toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Token validation failed',
        error
      };
    }
  }

  /**
   * Test security features
   */
  static async testSecurityFeatures(username: string = 'testuser'): Promise<AuthTestResult[]> {
    const { SecurityUtils } = await import('./securityUtils');
    const results: AuthTestResult[] = [];

    try {
      // Test login attempt tracking
      SecurityUtils.recordLoginAttempt(username, false);
      const failedAttempts = SecurityUtils.getRecentFailedAttempts(username);
      
      results.push({
        success: failedAttempts > 0,
        message: `Login attempt tracking: ${failedAttempts} failed attempts recorded`,
        data: { failedAttempts }
      });

      // Test lockout functionality
      const isLockedOut = SecurityUtils.isUserLockedOut(username);
      
      results.push({
        success: true,
        message: `User lockout status: ${isLockedOut ? 'locked out' : 'not locked out'}`,
        data: { isLockedOut }
      });

      // Test password validation
      const passwordTest = SecurityUtils.validatePasswordStrength('TestPassword123!');
      
      results.push({
        success: passwordTest.isValid,
        message: `Password validation: ${passwordTest.isValid ? 'passed' : 'failed'}`,
        data: { score: passwordTest.score, errors: passwordTest.errors }
      });

    } catch (error) {
      results.push({
        success: false,
        message: 'Security features test failed',
        error
      });
    }

    return results;
  }

  /**
   * Run all tests
   */
  static async runAllTests(): Promise<void> {
    console.group('🔐 Authentication Tests');
    
    try {
      // Test authentication flow
      console.group('Authentication Flow Tests');
      const authResults = await this.testAuthFlow();
      authResults.forEach(result => {
        console.log(result.success ? '✅' : '❌', result.message, result.data || result.error);
      });
      console.groupEnd();

      // Test token validation
      console.group('Token Validation Tests');
      const tokenResult = this.testTokenValidation();
      console.log(tokenResult.success ? '✅' : '❌', tokenResult.message, tokenResult.data || tokenResult.error);
      console.groupEnd();

      // Test security features
      console.group('Security Features Tests');
      const securityResults = await this.testSecurityFeatures();
      securityResults.forEach(result => {
        console.log(result.success ? '✅' : '❌', result.message, result.data || result.error);
      });
      console.groupEnd();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
    
    console.groupEnd();
  }
}

// Export convenience functions
export const testAuthFlow = AuthTestUtils.testAuthFlow;
export const runAllAuthTests = AuthTestUtils.runAllTests;
