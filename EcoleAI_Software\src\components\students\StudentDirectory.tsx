import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Search,
  Filter,
  Plus,
  Users,
  Eye,
  Edit,
  Trash2,
  Download,
  UserPlus
} from 'lucide-react';
import { apiClient } from '../../config/api';

interface Student {
  id: number;
  matricule: string;
  nom: string;
  prenom: string;
  date_naissance: string;
  sexe: string;
  adresse?: string;
  nom_parent: string;
  contact_parent: string;
  classe_id: number;
  is_active: boolean;
  created_at: string;
}

export default function StudentDirectory() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [classFilter, setClassFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('active');
  const [availableClasses, setAvailableClasses] = useState<any[]>([]);

  useEffect(() => {
    loadStudents();
    loadClasses();
  }, [searchQuery, classFilter, statusFilter]);

  const loadStudents = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();

      if (searchQuery) {
        params.append('search', searchQuery);
      }
      if (classFilter) {
        params.append('class_id', classFilter);
      }
      if (statusFilter === 'active') {
        params.append('active_only', 'true');
      }

      const queryString = params.toString();
      const url = queryString ? `/api/students/?${queryString}` : '/api/students/';

      const response = await apiClient.get(url);
      setStudents(response.data);
    } catch (error) {
      console.error('Error loading students:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadClasses = async () => {
    try {
      const response = await apiClient.get('/api/classes/');
      setAvailableClasses(response.data);
    } catch (error) {
      console.error('Error loading classes:', error);
    }
  };

  const getClassName = (classId: number) => {
    const classe = availableClasses.find(c => c.id === classId);
    return classe ? `${classe.nom} - ${classe.niveau}` : 'Classe inconnue';
  };

  const calculateAge = (birthDate: string) => {
    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  const handleDeleteStudent = async (studentId: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet élève ?')) {
      return;
    }

    try {
      await apiClient.delete(`/api/students/${studentId}`);
      loadStudents(); // Reload the list
    } catch (error) {
      console.error('Error deleting student:', error);
      alert('Erreur lors de la suppression de l\'élève');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Répertoire des Élèves</h1>
              <p className="mt-2 text-gray-600">
                Gérez et consultez les informations de tous les élèves inscrits
              </p>
            </div>
            <Link
              to="/students/enrollment"
              className="flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-opacity shadow-sm"
              style={{ backgroundColor: '#0a1186' }}
            >
              <UserPlus className="h-5 w-5 mr-2" />
              Inscrire un Élève
            </Link>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher un élève..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <select
              value={classFilter}
              onChange={(e) => setClassFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Toutes les classes</option>
              {availableClasses.map(classe => (
                <option key={classe.id} value={classe.id}>
                  {classe.nom} - {classe.niveau}
                </option>
              ))}
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="active">Élèves actifs</option>
              <option value="all">Tous les élèves</option>
              <option value="inactive">Élèves inactifs</option>
            </select>
          </div>
        </div>

        {/* Students List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">
                Liste des Élèves ({students.length})
              </h2>
              <button className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors">
                <Download className="h-4 w-4 mr-1" />
                Exporter
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Chargement...</span>
            </div>
          ) : students.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun élève trouvé</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || classFilter
                  ? 'Aucun élève ne correspond aux critères de recherche.'
                  : 'Aucun élève n\'est encore inscrit dans le système.'
                }
              </p>
              <Link
                to="/students/enrollment"
                className="inline-flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-opacity"
                style={{ backgroundColor: '#0a1186' }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Inscrire le Premier Élève
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead style={{ backgroundColor: '#0a1186' }}>
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Élève
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Matricule
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Classe
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Âge
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Parent/Tuteur
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {students.map((student) => (
                    <tr key={student.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {student.prenom.charAt(0)}{student.nom.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {student.prenom} {student.nom}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.sexe === 'M' ? 'Masculin' : 'Féminin'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {student.matricule}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getClassName(student.classe_id)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {calculateAge(student.date_naissance)} ans
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{student.nom_parent}</div>
                        <div className="text-sm text-gray-500">{student.contact_parent}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${student.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                          }`}>
                          {student.is_active ? 'Actif' : 'Inactif'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="Voir le dossier"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            className="text-green-600 hover:text-green-900 transition-colors"
                            title="Modifier"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteStudent(student.id)}
                            className="text-red-600 hover:text-red-900 transition-colors"
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
