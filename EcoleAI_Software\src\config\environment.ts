export interface AppConfig {
  apiBaseUrl: string;
  appName: string;
  tokenRefreshThreshold: number;
  sessionTimeout: number;
  enableSecureStorage: boolean;
  maxLoginAttempts: number;
  lockoutDuration: number;
  debugMode: boolean;
  logLevel: string;
}

class EnvironmentConfig {
  private static instance: EnvironmentConfig;
  private config: AppConfig;

  private constructor() {
    this.config = {
      apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
      appName: import.meta.env.VITE_APP_NAME || 'École AI',
      tokenRefreshThreshold: parseInt(import.meta.env.VITE_TOKEN_REFRESH_THRESHOLD) || 300000, // 5 minutes
      sessionTimeout: parseInt(import.meta.env.VITE_SESSION_TIMEOUT) || 3600000, // 1 hour
      enableSecureStorage: import.meta.env.VITE_ENABLE_SECURE_STORAGE === 'true',
      maxLoginAttempts: parseInt(import.meta.env.VITE_MAX_LOGIN_ATTEMPTS) || 5,
      lockoutDuration: parseInt(import.meta.env.VITE_LOCKOUT_DURATION) || 900000, // 15 minutes
      debugMode: import.meta.env.VITE_DEBUG_MODE === 'true',
      logLevel: import.meta.env.VITE_LOG_LEVEL || 'info'
    };

    this.validateConfig();
  }

  public static getInstance(): EnvironmentConfig {
    if (!EnvironmentConfig.instance) {
      EnvironmentConfig.instance = new EnvironmentConfig();
    }
    return EnvironmentConfig.instance;
  }

  public getConfig(): AppConfig {
    return { ...this.config };
  }

  public get apiBaseUrl(): string {
    return this.config.apiBaseUrl;
  }

  public get appName(): string {
    return this.config.appName;
  }

  public get tokenRefreshThreshold(): number {
    return this.config.tokenRefreshThreshold;
  }

  public get sessionTimeout(): number {
    return this.config.sessionTimeout;
  }

  public get enableSecureStorage(): boolean {
    return this.config.enableSecureStorage;
  }

  public get maxLoginAttempts(): number {
    return this.config.maxLoginAttempts;
  }

  public get lockoutDuration(): number {
    return this.config.lockoutDuration;
  }

  public get debugMode(): boolean {
    return this.config.debugMode;
  }

  public get logLevel(): string {
    return this.config.logLevel;
  }

  private validateConfig(): void {
    const errors: string[] = [];

    if (!this.config.apiBaseUrl) {
      errors.push('API_BASE_URL is required');
    }

    if (this.config.tokenRefreshThreshold < 60000) {
      errors.push('TOKEN_REFRESH_THRESHOLD must be at least 60 seconds');
    }

    if (this.config.sessionTimeout < 300000) {
      errors.push('SESSION_TIMEOUT must be at least 5 minutes');
    }

    if (this.config.maxLoginAttempts < 1) {
      errors.push('MAX_LOGIN_ATTEMPTS must be at least 1');
    }

    if (this.config.lockoutDuration < 60000) {
      errors.push('LOCKOUT_DURATION must be at least 1 minute');
    }

    if (errors.length > 0) {
      console.error('Configuration validation errors:', errors);
      if (!this.config.debugMode) {
        throw new Error(`Invalid configuration: ${errors.join(', ')}`);
      }
    }
  }

  public isProduction(): boolean {
    return import.meta.env.PROD;
  }

  public isDevelopment(): boolean {
    return import.meta.env.DEV;
  }
}

export const envConfig = EnvironmentConfig.getInstance();

export const {
  apiBaseUrl,
  appName,
  tokenRefreshThreshold,
  sessionTimeout,
  enableSecureStorage,
  maxLoginAttempts,
  lockoutDuration,
  debugMode,
  logLevel
} = envConfig;
