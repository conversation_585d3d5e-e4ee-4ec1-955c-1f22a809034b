import { useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface ActivityData {
  lastActivity: Date;
  totalClicks: number;
  totalKeystrokes: number;
  sessionStart: Date;
}

export function useActivityTracker() {
  const { isAuthenticated } = useAuth();
  const activityRef = useRef<ActivityData>({
    lastActivity: new Date(),
    totalClicks: 0,
    totalKeystrokes: 0,
    sessionStart: new Date()
  });

  useEffect(() => {
    if (!isAuthenticated) return;

    const handleActivity = (type: 'click' | 'keypress') => {
      activityRef.current.lastActivity = new Date();
      
      if (type === 'click') {
        activityRef.current.totalClicks++;
      } else if (type === 'keypress') {
        activityRef.current.totalKeystrokes++;
      }
    };

    const handleClick = () => handleActivity('click');
    const handleKeypress = () => handleActivity('keypress');

    // Add event listeners
    document.addEventListener('click', handleClick);
    document.addEventListener('keypress', handleKeypress);

    return () => {
      document.removeEventListener('click', handleClick);
      document.removeEventListener('keypress', handleKeypress);
    };
  }, [isAuthenticated]);

  const getActivityStats = () => {
    const now = new Date();
    const sessionDuration = now.getTime() - activityRef.current.sessionStart.getTime();
    const timeSinceLastActivity = now.getTime() - activityRef.current.lastActivity.getTime();

    return {
      ...activityRef.current,
      sessionDuration,
      timeSinceLastActivity,
      isActive: timeSinceLastActivity < 30000 // Active if activity within 30 seconds
    };
  };

  return {
    getActivityStats,
    resetSession: () => {
      activityRef.current = {
        lastActivity: new Date(),
        totalClicks: 0,
        totalKeystrokes: 0,
        sessionStart: new Date()
      };
    }
  };
}
