
from db import engine, Base
from models.user import User
from models.refresh_token import RefreshToken
from models.school_year import SchoolYear
from models.subject import Subject
from models.teacher import Teacher
from models.classe import Classe
from models.student import Student
from models.course import Course
from models.grade import Grade
from models.attendance import Attendance
from models.payment import Payment
from models.timetable import Timetable

def init():
    print("Creating tables...")
    Base.metadata.create_all(bind=engine)
    print("Done.")

if __name__ == "__main__":
    init()
