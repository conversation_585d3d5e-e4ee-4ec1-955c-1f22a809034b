import React, { useState } from 'react';
import { X, GraduationCap, AlertCircle } from 'lucide-react';
import { apiClient } from '../../../../config/api';

interface ClassModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (classe: any) => void;
  schoolYears: any[];
}

interface FormData {
  nom: string;
  niveau: string;
  annee_scolaire_id: number;
}

interface FormErrors {
  [key: string]: string;
}

export default function ClassModal({ isOpen, onClose, onSuccess, schoolYears }: ClassModalProps) {
  const [formData, setFormData] = useState<FormData>({
    nom: '',
    niveau: '',
    annee_scolaire_id: 0
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Le nom de la classe est requis';
    }

    if (!formData.niveau.trim()) {
      errors.niveau = 'Le niveau est requis';
    }

    if (!formData.annee_scolaire_id) {
      errors.annee_scolaire_id = 'L\'année scolaire est requise';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const response = await apiClient.post('/api/classes/', formData);
      onSuccess(response.data);
      onClose();
      resetForm();
    } catch (error: any) {
      console.error('Error creating class:', error);
      
      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        const newFormErrors: FormErrors = {};
        
        if (backendErrors.nom) {
          newFormErrors.nom = Array.isArray(backendErrors.nom) ? backendErrors.nom[0] : backendErrors.nom;
        }
        if (backendErrors.niveau) {
          newFormErrors.niveau = Array.isArray(backendErrors.niveau) ? backendErrors.niveau[0] : backendErrors.niveau;
        }
        if (backendErrors.annee_scolaire_id) {
          newFormErrors.annee_scolaire_id = Array.isArray(backendErrors.annee_scolaire_id) ? backendErrors.annee_scolaire_id[0] : backendErrors.annee_scolaire_id;
        }
        
        setFormErrors(newFormErrors);
      } else {
        setFormErrors({ general: 'Erreur lors de la création de la classe' });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      nom: '',
      niveau: '',
      annee_scolaire_id: schoolYears.find(sy => sy.is_active)?.id || 0
    });
    setFormErrors({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-1">
              Nouvelle classe
            </h2>
            <p className="text-sm text-gray-600">
              Créez une nouvelle classe
            </p>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
            type="button"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {formErrors.general && (
            <div className="flex items-center p-3 text-red-700 bg-red-100 border border-red-300 rounded-md">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span className="text-sm">{formErrors.general}</span>
            </div>
          )}

          {/* Nom de la classe */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom de la classe *
            </label>
            <input
              type="text"
              value={formData.nom}
              onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.nom 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="Ex: 6ème A"
            />
            {formErrors.nom && (
              <p className="mt-1 text-sm text-red-600">{formErrors.nom}</p>
            )}
          </div>

          {/* Niveau */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Niveau *
            </label>
            <select
              value={formData.niveau}
              onChange={(e) => setFormData({ ...formData, niveau: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.niveau 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
            >
              <option value="">Sélectionner un niveau</option>
              <option value="6ème">6ème</option>
              <option value="5ème">5ème</option>
              <option value="4ème">4ème</option>
              <option value="3ème">3ème</option>
              <option value="2nde">2nde</option>
              <option value="1ère">1ère</option>
              <option value="Terminale">Terminale</option>
            </select>
            {formErrors.niveau && (
              <p className="mt-1 text-sm text-red-600">{formErrors.niveau}</p>
            )}
          </div>

          {/* Année scolaire */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Année scolaire *
            </label>
            <select
              value={formData.annee_scolaire_id}
              onChange={(e) => setFormData({ ...formData, annee_scolaire_id: parseInt(e.target.value) })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.annee_scolaire_id 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
            >
              <option value={0}>Sélectionner une année scolaire</option>
              {schoolYears.map(schoolYear => (
                <option key={schoolYear.id} value={schoolYear.id}>
                  {schoolYear.annee} {schoolYear.is_active ? '(Active)' : '(Inactive)'}
                </option>
              ))}
            </select>
            {formErrors.annee_scolaire_id && (
              <p className="mt-1 text-sm text-red-600">{formErrors.annee_scolaire_id}</p>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200"
              disabled={isLoading}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 hover:opacity-90 disabled:opacity-50"
              style={{ backgroundColor: '#0a1186' }}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </div>
              ) : (
                'Créer'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
