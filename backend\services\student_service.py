# services/student_service.py
from typing import List, Dict, Optional
from datetime import date, datetime
from sqlalchemy.orm import Session
from crud.student import student
from crud.grade import grade
from crud.attendance import attendance
from crud.payment import payment
from models.student import Student

class StudentService:
    @staticmethod
    def generate_matricule(db: Session, year: int = None) -> str:
        """Generate a unique student matricule"""
        if year is None:
            year = datetime.now().year
        
        # Get the count of students for this year
        year_prefix = f"EAI{year}"
        existing_students = db.query(Student).filter(
            Student.matricule.like(f"{year_prefix}%")
        ).count()
        
        # Generate new matricule
        new_number = existing_students + 1
        return f"{year_prefix}{new_number:03d}"
    
    @staticmethod
    def get_student_academic_summary(db: Session, student_id: int) -> Dict:
        """Get comprehensive academic summary for a student"""
        student_obj = student.get(db, student_id)
        if not student_obj:
            return None
        
        # Get all grades
        grades = grade.get_by_student(db, eleve_id=student_id)
        
        # Calculate average grade
        if grades:
            total_grades = sum(g.note for g in grades)
            average_grade = total_grades / len(grades)
        else:
            average_grade = 0
        
        # Get attendance stats
        attendance_stats = attendance.get_attendance_stats_by_student(db, eleve_id=student_id)
        
        # Get payment summary
        total_paid = payment.get_total_by_student(db, eleve_id=student_id)
        pending_payments = payment.get_by_student(db, eleve_id=student_id)
        pending_amount = sum(p.montant for p in pending_payments if p.statut == 'pending')
        
        return {
            "student": student_obj,
            "academic": {
                "total_grades": len(grades),
                "average_grade": round(average_grade, 2),
                "grades_by_type": StudentService._group_grades_by_type(grades)
            },
            "attendance": attendance_stats,
            "financial": {
                "total_paid": total_paid,
                "pending_amount": pending_amount,
                "payment_status": "up_to_date" if pending_amount == 0 else "pending"
            }
        }
    
    @staticmethod
    def _group_grades_by_type(grades: List) -> Dict:
        """Group grades by type and calculate averages"""
        grades_by_type = {}
        for g in grades:
            if g.type not in grades_by_type:
                grades_by_type[g.type] = []
            grades_by_type[g.type].append(g.note)
        
        # Calculate averages for each type
        for grade_type, grade_list in grades_by_type.items():
            avg = sum(grade_list) / len(grade_list)
            grades_by_type[grade_type] = {
                "grades": grade_list,
                "average": round(avg, 2),
                "count": len(grade_list)
            }
        
        return grades_by_type
    
    @staticmethod
    def get_class_performance_report(db: Session, class_id: int) -> Dict:
        """Generate performance report for a class"""
        students_in_class = student.get_by_class(db, classe_id=class_id)
        
        if not students_in_class:
            return {"error": "No students found in this class"}
        
        class_stats = {
            "total_students": len(students_in_class),
            "students_summary": []
        }
        
        total_class_average = 0
        students_with_grades = 0
        
        for student_obj in students_in_class:
            student_grades = grade.get_by_student(db, eleve_id=student_obj.id)
            if student_grades:
                student_average = sum(g.note for g in student_grades) / len(student_grades)
                total_class_average += student_average
                students_with_grades += 1
            else:
                student_average = 0
            
            attendance_stats = attendance.get_attendance_stats_by_student(db, eleve_id=student_obj.id)
            total_attendance = sum(attendance_stats.values())
            present_count = attendance_stats.get('present', 0)
            attendance_rate = (present_count / total_attendance * 100) if total_attendance > 0 else 0
            
            class_stats["students_summary"].append({
                "student_id": student_obj.id,
                "name": f"{student_obj.prenom} {student_obj.nom}",
                "matricule": student_obj.matricule,
                "average_grade": round(student_average, 2),
                "total_grades": len(student_grades),
                "attendance_rate": round(attendance_rate, 2)
            })
        
        # Calculate class average
        if students_with_grades > 0:
            class_stats["class_average"] = round(total_class_average / students_with_grades, 2)
        else:
            class_stats["class_average"] = 0
        
        return class_stats
