[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "annotated-types==0.7.0",
    "anyio==4.10.0",
    "bcrypt==4.3.0",
    "cffi==1.17.1",
    "click==8.2.1",
    "colorama==0.4.6",
    "cryptography==45.0.6",
    "ecdsa==0.19.1",
    "fastapi==0.116.1",
    "h11==0.16.0",
    "httptools==0.6.4",
    "idna==3.10",
    "passlib==1.7.4",
    "psycopg2-binary>=2.9.10",
    "pyasn1==0.6.1",
    "pycparser==2.22",
    "pydantic[email]==2.11.7",
    "pydantic-core==2.33.2",
    "python-dotenv==1.1.1",
    "python-jose==3.5.0",
    "python-multipart==0.0.20",
    "pyyaml==6.0.2",
    "rsa==4.9.1",
    "six==1.17.0",
    "sniffio==1.3.1",
    "sqlalchemy>=2.0.42",
    "starlette==0.47.2",
    "typing-extensions==4.14.1",
    "typing-inspection==0.4.1",
    "uvicorn==0.35.0",
    "watchfiles==1.1.0",
    "websockets==15.0.1",
]
