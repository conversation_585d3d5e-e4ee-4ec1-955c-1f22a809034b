# routes/teachers.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from utils.security import get_current_active_user, get_db
from crud.teacher import teacher
from schemas.teacher import TeacherCreate, TeacherRead, TeacherUpdate

router = APIRouter(prefix="/api/teachers", tags=["Teachers"])

@router.get("/", response_model=List[TeacherRead])
async def get_teachers(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = Query(True, description="Get only active teachers"),
    search: str = Query(None, description="Search teachers by name"),
    specialite: str = Query(None, description="Filter by specialty"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all teachers with optional filters"""
    if search:
        return teacher.search_by_name(db, name=search)
    elif specialite:
        return teacher.get_by_specialite(db, specialite=specialite)
    elif active_only:
        return teacher.get_active(db)
    else:
        return teacher.get_multi(db, skip=skip, limit=limit)

@router.get("/{teacher_id}", response_model=TeacherRead)
async def get_teacher(
    teacher_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific teacher by ID"""
    db_teacher = teacher.get(db, teacher_id)
    if not db_teacher:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Teacher not found"
        )
    return db_teacher

@router.post("/", response_model=TeacherRead)
async def create_teacher(
    teacher_in: TeacherCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new teacher"""
    # Check if email already exists
    if teacher_in.email:
        existing = teacher.get_by_email(db, email=teacher_in.email)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Teacher with this email already exists"
            )
    
    return teacher.create(db, obj_in=teacher_in)

@router.put("/{teacher_id}", response_model=TeacherRead)
async def update_teacher(
    teacher_id: int,
    teacher_in: TeacherUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a teacher"""
    db_teacher = teacher.get(db, teacher_id)
    if not db_teacher:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Teacher not found"
        )
    
    # Check email uniqueness if updating email
    if teacher_in.email and teacher_in.email != db_teacher.email:
        existing = teacher.get_by_email(db, email=teacher_in.email)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Teacher with this email already exists"
            )
    
    return teacher.update(db, db_obj=db_teacher, obj_in=teacher_in)

@router.delete("/{teacher_id}")
async def delete_teacher(
    teacher_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a teacher (soft delete by setting is_active to False)"""
    db_teacher = teacher.get(db, teacher_id)
    if not db_teacher:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Teacher not found"
        )
    
    # Soft delete by setting is_active to False
    teacher.update(db, db_obj=db_teacher, obj_in={"is_active": False})
    return {"message": "Teacher deactivated successfully"}
