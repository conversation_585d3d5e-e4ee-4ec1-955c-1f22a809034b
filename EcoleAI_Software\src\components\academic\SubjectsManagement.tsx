import React, { useState, useEffect } from 'react';
import { BookOpen, Plus, Search, Filter, Edit, Eye, Award, Loader, Trash2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { apiClient } from '../../config/api';

interface Subject {
  id: number;
  nom: string;
  description?: string;
  coef: number;  // Backend uses 'coef' not 'coefficient'
  is_active?: boolean;  // Backend doesn't have is_active for subjects
  created_at?: string;
  updated_at?: string;
}

interface SubjectFormData {
  nom: string;
  description: string;
  coef: number;  // Backend expects 'coef' not 'coefficient'
}

export default function SubjectsManagement() {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  const [showModal, setShowModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formData, setFormData] = useState<SubjectFormData>({
    nom: '',
    description: '',
    coef: 1
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const { user } = useAuth();

  useEffect(() => {
    loadSubjects();
  }, []);

  const loadSubjects = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await apiClient.get('/api/subjects/');
      setSubjects(response.data);
    } catch (error) {
      console.error('Error loading subjects:', error);
      setError('Erreur lors du chargement des matières');
    } finally {
      setLoading(false);
    }
  };

  const filteredSubjects = subjects.filter(subject => {
    const matchesSearch = searchTerm === '' ||
      subject.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (subject.description && subject.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Since backend doesn't support is_active, all subjects are considered active
    return matchesSearch;
  });

  const canEdit = user?.role === 'admin';

  const resetForm = () => {
    setFormData({
      nom: '',
      description: '',
      coef: 1
    });
    setFormErrors({});
  };

  const handleAddNew = () => {
    if (!canEdit) return;
    setEditingSubject(null);
    resetForm();
    setShowModal(true);
  };

  const handleEdit = (subject: Subject) => {
    if (!canEdit) return;
    setEditingSubject(subject);
    setFormData({
      nom: subject.nom,
      description: subject.description || '',
      coef: subject.coef
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleView = (subject: Subject) => {
    console.log(`Voir la fiche de la matière ${subject.nom}`);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Le nom de la matière est requis';
    }
    if (formData.coef < 0.5 || formData.coef > 10) {
      errors.coef = 'Le coefficient doit être entre 0.5 et 10';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setFormLoading(true);
    try {
      if (editingSubject) {
        await apiClient.put(`/api/subjects/${editingSubject.id}/`, formData);
      } else {
        await apiClient.post('/api/subjects/', formData);
      }

      setShowModal(false);
      loadSubjects();
      resetForm();
    } catch (error: any) {
      console.error('Error saving subject:', error);

      // Handle validation errors from backend
      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        console.log('Backend validation errors:', backendErrors);

        // Map backend errors to form errors
        const newFormErrors: Record<string, string> = {};

        if (backendErrors.nom) {
          newFormErrors.nom = Array.isArray(backendErrors.nom) ? backendErrors.nom[0] : backendErrors.nom;
        }
        if (backendErrors.coef) {
          newFormErrors.coef = Array.isArray(backendErrors.coef) ? backendErrors.coef[0] : backendErrors.coef;
        }
        if (backendErrors.description) {
          newFormErrors.description = Array.isArray(backendErrors.description) ? backendErrors.description[0] : backendErrors.description;
        }

        // If we have specific field errors, show them
        if (Object.keys(newFormErrors).length > 0) {
          setFormErrors(newFormErrors);
        } else {
          // Generic validation error
          setError(`Erreur de validation: ${JSON.stringify(backendErrors)}`);
        }
      } else {
        setError('Erreur lors de la sauvegarde');
      }
    } finally {
      setFormLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!canEdit || !confirm('Êtes-vous sûr de vouloir supprimer cette matière ?')) return;

    try {
      await apiClient.delete(`/api/subjects/${id}/`);
      loadSubjects();
    } catch (error) {
      console.error('Error deleting subject:', error);
      setError('Erreur lors de la suppression');
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Matières</h1>
          <p className="text-gray-600 mt-1">
            {canEdit ? 'Management des matières académiques' : 'Consultation des matières académiques'}
          </p>
        </div>
        {canEdit && (
          <button
            onClick={handleAddNew}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
            style={{ backgroundColor: '#0a1186' }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle Matière
          </button>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={loadSubjects}
            className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
          >
            Réessayer
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-3 text-gray-600">Chargement des matières...</span>
        </div>
      ) : (
        <>
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Matières</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{subjects.length}</p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
                  <BookOpen className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Matières Actives</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {subjects.length}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
                  <BookOpen className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Coefficient Moyen</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {subjects.length > 0
                      ? (subjects.reduce((sum, s) => sum + s.coef, 0) / subjects.length).toFixed(1)
                      : 0
                    }
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
                  <Award className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Coefficients Uniques</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {new Set(subjects.map(s => s.coef)).size}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
                  <Filter className="h-6 w-6 text-green-700" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher une matière..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <select
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled
              >
                <option>Toutes les matières</option>
              </select>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Filter className="h-4 w-4 mr-2" />
                Plus de filtres
              </button>
            </div>
          </div>

          {/* Subjects Table */}
          {filteredSubjects.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-12 text-center">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune matière trouvée</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm
                  ? 'Aucune matière ne correspond aux critères de recherche.'
                  : 'Aucune matière n\'est encore configurée.'
                }
              </p>
              {canEdit && !searchTerm && (
                <button
                  onClick={handleAddNew}
                  className="inline-flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter la première matière
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead style={{ backgroundColor: '#0a1186' }}>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Matière
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Coefficient
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSubjects.map((subject) => (
                      <tr key={subject.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <BookOpen className="h-5 w-5 text-gray-600" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {subject.nom}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {subject.id}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 max-w-xs truncate">
                            {subject.description || 'Aucune description'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            {subject.coef}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleView(subject)}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            {canEdit && (
                              <>
                                <button
                                  onClick={() => handleEdit(subject)}
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete(subject.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}

      {/* Modal Form */}
      {showModal && canEdit && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">
                {editingSubject ? 'Modifier la matière' : 'Nouvelle matière'}
              </h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Nom de la matière */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom de la matière *
                </label>
                <input
                  type="text"
                  value={formData.nom}
                  onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.nom ? 'border-red-500' : 'border-gray-300'
                    }`}
                  placeholder="Ex: Mathématiques"
                />
                {formErrors.nom && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.nom}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Description de la matière (optionnel)"
                />
              </div>

              {/* Coefficient */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Coefficient *
                </label>
                <input
                  type="number"
                  min="0.5"
                  max="10"
                  step="0.5"
                  value={formData.coef}
                  onChange={(e) => setFormData({ ...formData, coef: parseFloat(e.target.value) || 1 })}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.coef ? 'border-red-500' : 'border-gray-300'
                    }`}
                  placeholder="1"
                />
                {formErrors.coef && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.coef}</p>
                )}
                <p className="mt-1 text-sm text-gray-500">
                  Le coefficient détermine l'importance de la matière dans le calcul des moyennes
                </p>
              </div>



              <div className="flex justify-end space-x-3 pt-6">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                  disabled={formLoading}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 disabled:opacity-50"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  {formLoading ? 'Enregistrement...' : (editingSubject ? 'Modifier' : 'Ajouter')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
