# routes/payments.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from utils.security import get_current_active_user, get_db
from crud.payment import payment
from schemas.payment import PaymentCreate, PaymentRead, PaymentUpdate
from services.financial_service import FinancialService

router = APIRouter(prefix="/api/payments", tags=["Payments"])

@router.get("/", response_model=List[PaymentRead])
async def get_payments(
    skip: int = 0,
    limit: int = 100,
    student_id: int = Query(None, description="Filter by student"),
    status: str = Query(None, description="Filter by payment status"),
    motif: str = Query(None, description="Filter by payment reason"),
    start_date: str = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(None, description="End date (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all payments with optional filters"""
    if student_id:
        return payment.get_by_student(db, eleve_id=student_id)
    elif status:
        return payment.get_by_status(db, statut=status)
    elif motif:
        return payment.get_by_motif(db, motif=motif)
    elif start_date and end_date:
        from datetime import datetime
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        return payment.get_by_date_range(db, start_date=start_date_obj, end_date=end_date_obj)
    else:
        return payment.get_multi(db, skip=skip, limit=limit)

@router.get("/dashboard")
async def get_financial_dashboard(
    start_date: str = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(None, description="End date (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get comprehensive financial dashboard data"""
    from datetime import datetime
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
    
    dashboard = FinancialService.get_financial_dashboard(db, start_date_obj, end_date_obj)
    return dashboard

@router.get("/pending", response_model=List[PaymentRead])
async def get_pending_payments(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all pending payments"""
    return payment.get_pending_payments(db)

@router.get("/{payment_id}", response_model=PaymentRead)
async def get_payment(
    payment_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific payment by ID"""
    db_payment = payment.get(db, payment_id)
    if not db_payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    return db_payment

@router.post("/", response_model=PaymentRead)
async def create_payment(
    payment_in: PaymentCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new payment"""
    # Generate reference if not provided
    if not payment_in.reference:
        payment_in.reference = FinancialService.generate_payment_reference(db)
    
    return payment.create(db, obj_in=payment_in)

@router.post("/bulk-monthly-fees")
async def create_monthly_fees(
    class_id: int,
    amount: float,
    month: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create monthly fee payments for all students in a class"""
    created_payments = FinancialService.create_monthly_fees(db, class_id, amount, month)
    return {
        "message": f"Created {len(created_payments)} monthly fee payments",
        "payments_created": len(created_payments)
    }

@router.put("/{payment_id}", response_model=PaymentRead)
async def update_payment(
    payment_id: int,
    payment_in: PaymentUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a payment"""
    db_payment = payment.get(db, payment_id)
    if not db_payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    return payment.update(db, db_obj=db_payment, obj_in=payment_in)

@router.delete("/{payment_id}")
async def delete_payment(
    payment_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a payment"""
    db_payment = payment.get(db, payment_id)
    if not db_payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    # Only allow deletion of pending payments
    if db_payment.statut != 'pending':
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only delete pending payments"
        )
    
    payment.remove(db, id=payment_id)
    return {"message": "Payment deleted successfully"}
