# schemas/payment.py
from pydantic import BaseModel, validator
from datetime import date, datetime
from typing import Optional

class PaymentBase(BaseModel):
    eleve_id: int
    montant: float
    motif: str
    date_paiement: Optional[date] = None
    statut: Optional[str] = "paid"
    methode_paiement: Optional[str] = None
    reference: Optional[str] = None
    commentaire: Optional[str] = None

    @validator('montant')
    def validate_montant(cls, v):
        if v <= 0:
            raise ValueError('Payment amount must be positive')
        return v

    @validator('motif')
    def validate_motif(cls, v):
        valid_motifs = [
            'registration', 'monthly_fee', 'cafeteria', 'transport', 
            'books', 'uniform', 'activities', 'exam_fee', 'other'
        ]
        if v.lower() not in valid_motifs:
            raise ValueError(f'Payment reason must be one of: {", ".join(valid_motifs)}')
        return v.lower()

    @validator('statut')
    def validate_statut(cls, v):
        valid_statuts = ['paid', 'partial', 'pending', 'cancelled']
        if v and v.lower() not in valid_statuts:
            raise ValueError(f'Payment status must be one of: {", ".join(valid_statuts)}')
        return v.lower() if v else "paid"

class PaymentCreate(PaymentBase):
    pass

class PaymentUpdate(BaseModel):
    eleve_id: Optional[int] = None
    montant: Optional[float] = None
    motif: Optional[str] = None
    date_paiement: Optional[date] = None
    statut: Optional[str] = None
    methode_paiement: Optional[str] = None
    reference: Optional[str] = None
    commentaire: Optional[str] = None

class PaymentRead(PaymentBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
