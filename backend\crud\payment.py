# crud/payment.py
from typing import List, Optional
from datetime import date
from sqlalchemy.orm import Session
from sqlalchemy import func
from crud.base import CRUDBase
from models.payment import Payment
from schemas.payment import PaymentCreate, PaymentUpdate

class CRUDPayment(CRUDBase[Payment, PaymentCreate, PaymentUpdate]):
    def get_by_student(self, db: Session, *, eleve_id: int) -> List[Payment]:
        return db.query(Payment).filter(Payment.eleve_id == eleve_id).all()
    
    def get_by_status(self, db: Session, *, statut: str) -> List[Payment]:
        return db.query(Payment).filter(Payment.statut == statut).all()
    
    def get_by_motif(self, db: Session, *, motif: str) -> List[Payment]:
        return db.query(Payment).filter(Payment.motif == motif).all()
    
    def get_by_date_range(
        self, db: Session, *, start_date: date, end_date: date
    ) -> List[Payment]:
        return db.query(Payment).filter(
            Payment.date_paiement >= start_date,
            Payment.date_paiement <= end_date
        ).all()
    
    def get_total_by_student(self, db: Session, *, eleve_id: int) -> Optional[float]:
        result = db.query(func.sum(Payment.montant)).filter(
            Payment.eleve_id == eleve_id,
            Payment.statut == 'paid'
        ).scalar()
        return float(result) if result else 0.0
    
    def get_pending_payments(self, db: Session) -> List[Payment]:
        return db.query(Payment).filter(Payment.statut == 'pending').all()
    
    def get_by_reference(self, db: Session, *, reference: str) -> Optional[Payment]:
        return db.query(Payment).filter(Payment.reference == reference).first()

payment = CRUDPayment(Payment)
