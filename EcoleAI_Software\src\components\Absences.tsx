import React, { useState } from 'react';
import { Calendar, Plus, Search, Filter, AlertTriangle, Users, TrendingUp, Eye } from 'lucide-react';
import { useNotifications } from '../contexts/NotificationContext';

interface Absence {
  id: string;
  studentId: string;
  studentName: string;
  studentClass: string;
  date: string;
  reason: string;
  justified: boolean;
  justification?: string;
  enteredBy: string;
  entryDate: string;
}

const mockAbsences: Absence[] = [
  {
    id: '1',
    studentId: '1',
    studentName: 'RASOLONDRAIBE Solo',
    studentClass: '6ème A',
    date: '2024-01-15',
    reason: 'Maladie',
    justified: true,
    justification: 'Certificat médical',
    enteredBy: 'Sec<PERSON>taire',
    entryDate: '2024-01-15'
  },
  {
    id: '2',
    studentId: '2',
    studentName: 'RAVELOJAONA Marie',
    studentClass: 'CM2 B',
    date: '2024-01-14',
    reason: 'Personnel',
    justified: false,
    enteredBy: 'Se<PERSON><PERSON><PERSON>',
    entryDate: '2024-01-14'
  },
  {
    id: '3',
    studentId: '1',
    studentName: 'RASOLONDRAIBE Solo',
    studentClass: '6ème A',
    date: '2024-01-13',
    reason: 'Maladie',
    justified: true,
    justification: 'Certificat médical',
    enteredBy: 'Secrétaire',
    entryDate: '2024-01-13'
  },
  {
    id: '4',
    studentId: '1',
    studentName: 'RASOLONDRAIBE Solo',
    studentClass: '6ème A',
    date: '2024-01-12',
    reason: 'Personnel',
    justified: false,
    enteredBy: 'Secrétaire',
    entryDate: '2024-01-12'
  }
];

export default function Absences() {
  const [absences, setAbsences] = useState<Absence[]>(mockAbsences);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedMonth, setSelectedMonth] = useState('');
  const [showModal, setShowModal] = useState(false);
  const { addNotification } = useNotifications();

  const classes = ['6ème A', '6ème B', '5ème A', '5ème C', 'CM2 A', 'CM2 B'];
  const months = ['Janvier 2024', 'Février 2024', 'Mars 2024'];

  const filteredAbsences = absences.filter(absence => {
    const matchesSearch = absence.studentName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClass === '' || absence.studentClass === selectedClass;
    return matchesSearch && matchesClass;
  });

  // Calculer les statistiques
  const totalAbsences = filteredAbsences.length;
  const justifiedAbsences = filteredAbsences.filter(a => a.justified).length;
  const unjustifiedAbsences = totalAbsences - justifiedAbsences;
  
  // Détecter les absences fréquentes
  const absencesByStudent = filteredAbsences.reduce((acc, absence) => {
    if (!acc[absence.studentId]) {
      acc[absence.studentId] = [];
    }
    acc[absence.studentId].push(absence);
    return acc;
  }, {} as Record<string, Absence[]>);

  const frequentAbsentees = Object.entries(absencesByStudent)
    .filter(([_, absences]) => absences.length >= 3)
    .map(([studentId, studentAbsences]) => ({
      studentId,
      studentName: studentAbsences[0].studentName,
      studentClass: studentAbsences[0].studentClass,
      count: studentAbsences.length
    }));

  const handleAddAbsence = (formData: any) => {
    const newAbsence: Absence = {
      id: Date.now().toString(),
      studentId: formData.studentId,
      studentName: formData.studentName,
      studentClass: formData.studentClass,
      date: formData.date,
      reason: formData.reason,
      justified: formData.justified,
      justification: formData.justification,
      enteredBy: 'Secrétaire',
      entryDate: new Date().toISOString().split('T')[0]
    };

    setAbsences(prev => [newAbsence, ...prev]);
    
    // Vérifier si cela déclenche une alerte d'absence fréquente
    const studentAbsences = absences.filter(a => a.studentId === formData.studentId).length + 1;
    if (studentAbsences >= 5) {
      addNotification({
        type: 'absence',
        title: 'Absence fréquente détectée',
        message: `L'élève ${formData.studentName} cumule ${studentAbsences} absences`
      });
    }
    
    setShowModal(false);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Absences</h1>
          <p className="text-gray-600 mt-1">Suivi des absences des élèves</p>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
          style={{ backgroundColor: '#0a1186' }}
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle Absence
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Absences</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{totalAbsences}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <Calendar className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Justifiées</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{justifiedAbsences}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <TrendingUp className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Non Justifiées</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{unjustifiedAbsences}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <AlertTriangle className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Absents Fréquents</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{frequentAbsentees.length}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <Users className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Frequent Absentees Alert */}
      {frequentAbsentees.length > 0 && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-orange-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-orange-900">Absences Fréquentes Détectées</h3>
              <div className="mt-2 text-sm text-orange-700">
                <p>Les élèves suivants ont des absences répétées :</p>
                <ul className="list-disc list-inside mt-1">
                  {frequentAbsentees.map(student => (
                    <li key={student.studentId}>
                      {student.studentName} ({student.studentClass}) - {student.count} absences
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un élève..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
          >
            <option value="">Toutes les classes</option>
            {classes.map(cls => (
              <option key={cls} value={cls}>{cls}</option>
            ))}
          </select>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(e.target.value)}
          >
            <option value="">Tous les mois</option>
            {months.map(month => (
              <option key={month} value={month}>{month}</option>
            ))}
          </select>
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <Filter className="h-4 w-4 mr-2" />
            Plus de filtres
          </button>
        </div>
      </div>

      {/* Absences Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead style={{ backgroundColor: '#0a1186' }}>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Élève
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Date d'Absence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Motif
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Justifiée
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Justification
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Saisie par
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAbsences.map((absence) => (
                <tr key={absence.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {absence.studentName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {absence.studentClass}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(absence.date).toLocaleDateString('fr-FR')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {absence.reason}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      absence.justified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {absence.justified ? 'Oui' : 'Non'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {absence.justification || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>
                      {absence.enteredBy}
                      <div className="text-xs text-gray-400">
                        {new Date(absence.entryDate).toLocaleDateString('fr-FR')}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-blue-600 hover:text-blue-900 p-1 rounded"
                      title="Voir les détails"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Absence Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Nouvelle Absence</h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                handleAddAbsence({
                  studentId: formData.get('studentId'),
                  studentName: formData.get('studentName'),
                  studentClass: formData.get('studentClass'),
                  date: formData.get('date'),
                  reason: formData.get('reason'),
                  justified: formData.get('justified') === 'on',
                  justification: formData.get('justification')
                });
              }}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Élève</label>
                  <select
                    name="studentId"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">Sélectionner un élève</option>
                    <option value="1" data-name="RASOLONDRAIBE Solo" data-class="6ème A">RASOLONDRAIBE Solo (6ème A)</option>
                    <option value="2" data-name="RAVELOJAONA Marie" data-class="CM2 B">RAVELOJAONA Marie (CM2 B)</option>
                  </select>
                  <input type="hidden" name="studentName" />
                  <input type="hidden" name="studentClass" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date d'absence</label>
                  <input
                    type="date"
                    name="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    defaultValue={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Motif</label>
                <select
                  name="reason"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Sélectionner un motif</option>
                  <option value="Maladie">Maladie</option>
                  <option value="Personnel">Personnel</option>
                  <option value="Familial">Familial</option>
                  <option value="Autre">Autre</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="justified"
                  id="justified"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="justified" className="ml-2 text-sm font-medium text-gray-700">
                  Absence justifiée
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Justification (optionnel)</label>
                <textarea
                  name="justification"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Détails de la justification (certificat médical, etc.)"
                ></textarea>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  Enregistrer
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}