# models/grade.py
from sqlalchemy import Column, Integer, String, Float, ForeignKey, DateTime, Date
from sqlalchemy.orm import relationship
from datetime import datetime, date
from db import Base

class Grade(Base):
    __tablename__ = "grades"

    id = Column(Integer, primary_key=True, index=True)
    eleve_id = Column(Integer, ForeignKey("students.id"), nullable=False)
    cours_id = Column(Integer, ForeignKey("courses.id"), nullable=False)
    note = Column(Float, nullable=False)  # Grade value (0-20 typically)
    type = Column(String, nullable=False)  # DS, exam, test, homework, etc.
    date = Column(Date, nullable=False, default=date.today)
    commentaire = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    student = relationship("Student", back_populates="grades")
    course = relationship("Course", back_populates="grades")
