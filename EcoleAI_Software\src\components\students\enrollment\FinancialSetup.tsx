import React, { useState, useEffect, useCallback } from 'react';
import {
  DollarSign,
  CreditCard,
  Calendar,
  Plus,
  Trash2,
  CheckCircle,
  AlertCircle,
  Receipt
} from 'lucide-react';

interface FinancialSetupProps {
  enrollmentData: any;
  updateEnrollmentData: (section: string, data: any) => void;
  updateStepCompletion: (stepId: string, completed: boolean) => void;
  currentStep: any;
}

interface AdditionalFee {
  id: string;
  type: string;
  amount: number;
  description: string;
}

interface FinancialData {
  registrationFee: number;
  registrationPaid: boolean;
  registrationMethod: string;
  monthlyFee: number;
  monthlyFeeSetup: boolean;
  additionalFees: AdditionalFee[];
}

const FEE_TYPES = [
  { value: 'books', label: 'Livres et Fournitures', defaultAmount: 50 },
  { value: 'uniform', label: 'Uniforme Scolaire', defaultAmount: 75 },
  { value: 'transport', label: 'Transport Scolaire', defaultAmount: 30 },
  { value: 'cafeteria', label: 'Cantine', defaultAmount: 40 },
  { value: 'activities', label: 'Activités Extrascolaires', defaultAmount: 25 },
  { value: 'exam_fee', label: 'Frais d\'Examen', defaultAmount: 20 },
  { value: 'other', label: 'Autres Frais', defaultAmount: 0 },
];

const PAYMENT_METHODS = [
  { value: 'cash', label: 'Espèces' },
  { value: 'bank_transfer', label: 'Virement Bancaire' },
  { value: 'check', label: 'Chèque' },
  { value: 'card', label: 'Carte Bancaire' },
  { value: 'mobile_money', label: 'Mobile Money' },
];

export default function FinancialSetup({
  enrollmentData,
  updateEnrollmentData,
  updateStepCompletion,
  currentStep
}: FinancialSetupProps) {
  const [financialData, setFinancialData] = useState<FinancialData>({
    registrationFee: 100,
    registrationPaid: false,
    registrationMethod: '',
    monthlyFee: 50,
    monthlyFeeSetup: true,
    additionalFees: [],
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    // Load existing data if available
    if (enrollmentData.financial) {
      setFinancialData(prev => ({ ...prev, ...enrollmentData.financial }));
    }
  }, [enrollmentData]);

  useEffect(() => {
    // Debounce the validation and data update to prevent glitching
    const timeoutId = setTimeout(() => {
      const isValid = validateFinancialSetup();
      updateStepCompletion(currentStep.id, isValid);
      updateEnrollmentData('financial', financialData);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [financialData]);

  const validateFinancialSetup = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (financialData.registrationFee <= 0) {
      newErrors.registrationFee = 'Les frais d\'inscription doivent être supérieurs à 0';
    }

    if (financialData.registrationPaid && !financialData.registrationMethod) {
      newErrors.registrationMethod = 'Veuillez sélectionner une méthode de paiement';
    }

    if (financialData.monthlyFeeSetup && financialData.monthlyFee <= 0) {
      newErrors.monthlyFee = 'Les frais mensuels doivent être supérieurs à 0';
    }

    // Validate additional fees
    financialData.additionalFees.forEach((fee, index) => {
      if (fee.amount <= 0) {
        newErrors[`additionalFee_${index}_amount`] = 'Le montant doit être supérieur à 0';
      }
      if (!fee.description.trim()) {
        newErrors[`additionalFee_${index}_description`] = 'La description est requise';
      }
    });

    // Only update errors if they have actually changed
    const errorsChanged = JSON.stringify(newErrors) !== JSON.stringify(errors);
    if (errorsChanged) {
      setErrors(newErrors);
    }

    return Object.keys(newErrors).length === 0;
  };

  const updateFinancialField = useCallback((field: keyof FinancialData, value: any) => {
    setFinancialData(prev => ({ ...prev, [field]: value }));
  }, []);

  const addAdditionalFee = useCallback(() => {
    const newFee: AdditionalFee = {
      id: Date.now().toString(),
      type: 'other',
      amount: 0,
      description: '',
    };
    setFinancialData(prev => ({
      ...prev,
      additionalFees: [...prev.additionalFees, newFee]
    }));
  }, []);

  const updateAdditionalFee = useCallback((id: string, field: keyof AdditionalFee, value: any) => {
    setFinancialData(prev => ({
      ...prev,
      additionalFees: prev.additionalFees.map(fee =>
        fee.id === id ? { ...fee, [field]: value } : fee
      )
    }));
  }, []);

  const removeAdditionalFee = useCallback((id: string) => {
    setFinancialData(prev => ({
      ...prev,
      additionalFees: prev.additionalFees.filter(fee => fee.id !== id)
    }));
  }, []);

  const handleFeeTypeChange = useCallback((id: string, type: string) => {
    const feeType = FEE_TYPES.find(ft => ft.value === type);
    updateAdditionalFee(id, 'type', type);
    if (feeType && feeType.defaultAmount > 0) {
      updateAdditionalFee(id, 'amount', feeType.defaultAmount);
      updateAdditionalFee(id, 'description', feeType.label);
    }
  }, [updateAdditionalFee]);

  const getTotalAmount = useCallback(() => {
    const registration = financialData.registrationPaid ? financialData.registrationFee : 0;
    const additional = financialData.additionalFees.reduce((sum, fee) => sum + fee.amount, 0);
    return registration + additional;
  }, [financialData.registrationPaid, financialData.registrationFee, financialData.additionalFees]);

  const getMonthlyTotal = useCallback(() => {
    return financialData.monthlyFeeSetup ? financialData.monthlyFee : 0;
  }, [financialData.monthlyFeeSetup, financialData.monthlyFee]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Configuration Financière</h2>
        <p className="text-sm text-gray-600 mt-1">
          Configurez les frais d'inscription et les paiements récurrents pour l'élève
        </p>
      </div>

      {/* Registration Fee */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Receipt className="h-5 w-5 mr-2 text-blue-600" />
          Frais d'Inscription
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Montant des Frais d'Inscription (Ar)
            </label>
            <input
              type="number"
              min="0"
              step="0.01"
              value={financialData.registrationFee}
              onChange={(e) => updateFinancialField('registrationFee', parseFloat(e.target.value) || 0)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.registrationFee ? 'border-red-300' : 'border-gray-300'
                }`}
            />
            {errors.registrationFee && (
              <p className="mt-1 text-sm text-red-600">{errors.registrationFee}</p>
            )}
          </div>

          <div>
            <label className="flex items-center space-x-2 mt-6">
              <input
                type="checkbox"
                checked={financialData.registrationPaid}
                onChange={(e) => updateFinancialField('registrationPaid', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                Frais d'inscription payés lors de l'inscription
              </span>
            </label>
          </div>
        </div>

        {financialData.registrationPaid && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Méthode de Paiement
            </label>
            <select
              value={financialData.registrationMethod}
              onChange={(e) => updateFinancialField('registrationMethod', e.target.value)}
              className={`w-full md:w-1/2 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.registrationMethod ? 'border-red-300' : 'border-gray-300'
                }`}
            >
              <option value="">Sélectionner une méthode</option>
              {PAYMENT_METHODS.map(method => (
                <option key={method.value} value={method.value}>
                  {method.label}
                </option>
              ))}
            </select>
            {errors.registrationMethod && (
              <p className="mt-1 text-sm text-red-600">{errors.registrationMethod}</p>
            )}
          </div>
        )}
      </div>

      {/* Monthly Fees */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Calendar className="h-5 w-5 mr-2 text-green-600" />
          Frais Mensuels
        </h3>

        <div className="space-y-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={financialData.monthlyFeeSetup}
              onChange={(e) => updateFinancialField('monthlyFeeSetup', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700">
              Configurer les frais mensuels récurrents
            </span>
          </label>

          {financialData.monthlyFeeSetup && (
            <div className="md:w-1/2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Montant Mensuel (Ar)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={financialData.monthlyFee}
                onChange={(e) => updateFinancialField('monthlyFee', parseFloat(e.target.value) || 0)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.monthlyFee ? 'border-red-300' : 'border-gray-300'
                  }`}
              />
              {errors.monthlyFee && (
                <p className="mt-1 text-sm text-red-600">{errors.monthlyFee}</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Additional Fees */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <DollarSign className="h-5 w-5 mr-2 text-purple-600" />
            Frais Additionnels
          </h3>
          <button
            onClick={addAdditionalFee}
            className="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
          >
            <Plus className="h-4 w-4 mr-1" />
            Ajouter un Frais
          </button>
        </div>

        {financialData.additionalFees.length === 0 ? (
          <p className="text-gray-500 text-center py-4">
            Aucun frais additionnel configuré. Cliquez sur "Ajouter un Frais" pour en ajouter.
          </p>
        ) : (
          <div className="space-y-4">
            {financialData.additionalFees.map((fee, index) => (
              <div key={fee.id} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type de Frais
                    </label>
                    <select
                      value={fee.type}
                      onChange={(e) => handleFeeTypeChange(fee.id, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {FEE_TYPES.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Montant (Ar)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={fee.amount}
                      onChange={(e) => updateAdditionalFee(fee.id, 'amount', parseFloat(e.target.value) || 0)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors[`additionalFee_${index}_amount`] ? 'border-red-300' : 'border-gray-300'
                        }`}
                    />
                    {errors[`additionalFee_${index}_amount`] && (
                      <p className="mt-1 text-sm text-red-600">{errors[`additionalFee_${index}_amount`]}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <input
                      type="text"
                      value={fee.description}
                      onChange={(e) => updateAdditionalFee(fee.id, 'description', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors[`additionalFee_${index}_description`] ? 'border-red-300' : 'border-gray-300'
                        }`}
                      placeholder="Description du frais"
                    />
                    {errors[`additionalFee_${index}_description`] && (
                      <p className="mt-1 text-sm text-red-600">{errors[`additionalFee_${index}_description`]}</p>
                    )}
                  </div>

                  <div className="flex items-end">
                    <button
                      onClick={() => removeAdditionalFee(fee.id)}
                      className="w-full px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 transition-colors"
                    >
                      <Trash2 className="h-4 w-4 mx-auto" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Financial Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-4">Résumé Financier</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Paiements Initiaux</h4>
            <div className="space-y-1 text-sm">
              {financialData.registrationPaid && (
                <div className="flex justify-between">
                  <span>Frais d'inscription:</span>
                  <span className="font-medium">{financialData.registrationFee.toFixed(2)} Ar</span>
                </div>
              )}
              {financialData.additionalFees.map(fee => (
                <div key={fee.id} className="flex justify-between">
                  <span>{fee.description}:</span>
                  <span className="font-medium">{fee.amount.toFixed(2)} Ar</span>
                </div>
              ))}
              <div className="border-t border-blue-300 pt-1 flex justify-between font-medium">
                <span>Total Initial:</span>
                <span>{getTotalAmount().toFixed(2)} Ar</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-blue-800 mb-2">Paiements Récurrents</h4>
            <div className="space-y-1 text-sm">
              {financialData.monthlyFeeSetup ? (
                <div className="flex justify-between">
                  <span>Frais mensuels:</span>
                  <span className="font-medium">{financialData.monthlyFee.toFixed(2)} Ar / mois</span>
                </div>
              ) : (
                <span className="text-gray-600">Aucun frais mensuel configuré</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Validation Status */}
      {Object.keys(errors).length === 0 && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
            <div>
              <h4 className="font-medium text-green-900">Configuration Financière Valide</h4>
              <p className="text-sm text-green-700">
                La configuration financière est complète et valide.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
