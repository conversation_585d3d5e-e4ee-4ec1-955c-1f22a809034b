# crud/timetable.py
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from datetime import date, time
from crud.base import CRUDBase
from models.timetable import Timetable
from models.classe import Classe
from models.subject import Subject
from models.teacher import Teacher
from models.school_year import SchoolYear
from schemas.timetable import TimetableCreate, TimetableUpdate

class CRUDTimetable(CRUDBase[Timetable, TimetableCreate, TimetableUpdate]):
    
    def get_with_details(self, db: Session, id: int) -> Optional[Timetable]:
        """Get timetable entry with all related data loaded"""
        return db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher),
            joinedload(Timetable.school_year)
        ).filter(Timetable.id == id).first()
    
    def get_multi_with_details(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[Timetable]:
        """Get multiple timetable entries with all related data"""
        return db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher),
            joinedload(Timetable.school_year)
        ).offset(skip).limit(limit).all()
    
    def get_by_class(self, db: Session, *, classe_id: int) -> List[Timetable]:
        """Get all timetable entries for a specific class"""
        return db.query(Timetable).options(
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher)
        ).filter(
            Timetable.classe_id == classe_id,
            Timetable.is_active == True
        ).order_by(Timetable.jour_semaine, Timetable.heure_debut).all()
    
    def get_by_teacher(self, db: Session, *, enseignant_id: int) -> List[Timetable]:
        """Get all timetable entries for a specific teacher"""
        return db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject)
        ).filter(
            Timetable.enseignant_id == enseignant_id,
            Timetable.is_active == True
        ).order_by(Timetable.jour_semaine, Timetable.heure_debut).all()
    
    def get_by_subject(self, db: Session, *, matiere_id: int) -> List[Timetable]:
        """Get all timetable entries for a specific subject"""
        return db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.teacher)
        ).filter(
            Timetable.matiere_id == matiere_id,
            Timetable.is_active == True
        ).order_by(Timetable.jour_semaine, Timetable.heure_debut).all()
    
    def get_by_school_year(self, db: Session, *, annee_scolaire_id: int) -> List[Timetable]:
        """Get all timetable entries for a specific school year"""
        return db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher)
        ).filter(
            Timetable.annee_scolaire_id == annee_scolaire_id,
            Timetable.is_active == True
        ).order_by(Timetable.jour_semaine, Timetable.heure_debut).all()
    
    def get_by_day(self, db: Session, *, jour_semaine: str) -> List[Timetable]:
        """Get all timetable entries for a specific day"""
        return db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher)
        ).filter(
            Timetable.jour_semaine == jour_semaine,
            Timetable.is_active == True
        ).order_by(Timetable.heure_debut).all()
    
    def get_by_room(self, db: Session, *, salle: str) -> List[Timetable]:
        """Get all timetable entries for a specific room"""
        return db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher)
        ).filter(
            Timetable.salle.ilike(f"%{salle}%"),
            Timetable.is_active == True
        ).order_by(Timetable.jour_semaine, Timetable.heure_debut).all()
    
    def check_teacher_conflict(
        self,
        db: Session,
        *,
        enseignant_id: int,
        jour_semaine: str,
        heure_debut: time,
        heure_fin: time,
        exclude_id: Optional[int] = None
    ) -> List[Timetable]:
        """Check for teacher scheduling conflicts"""
        query = db.query(Timetable).filter(
            Timetable.enseignant_id == enseignant_id,
            Timetable.jour_semaine == jour_semaine,
            Timetable.is_active == True,
            or_(
                and_(Timetable.heure_debut <= heure_debut, Timetable.heure_fin > heure_debut),
                and_(Timetable.heure_debut < heure_fin, Timetable.heure_fin >= heure_fin),
                and_(Timetable.heure_debut >= heure_debut, Timetable.heure_fin <= heure_fin)
            )
        )
        
        if exclude_id:
            query = query.filter(Timetable.id != exclude_id)
            
        return query.all()
    
    def check_class_conflict(
        self,
        db: Session,
        *,
        classe_id: int,
        jour_semaine: str,
        heure_debut: time,
        heure_fin: time,
        exclude_id: Optional[int] = None
    ) -> List[Timetable]:
        """Check for class scheduling conflicts"""
        query = db.query(Timetable).filter(
            Timetable.classe_id == classe_id,
            Timetable.jour_semaine == jour_semaine,
            Timetable.is_active == True,
            or_(
                and_(Timetable.heure_debut <= heure_debut, Timetable.heure_fin > heure_debut),
                and_(Timetable.heure_debut < heure_fin, Timetable.heure_fin >= heure_fin),
                and_(Timetable.heure_debut >= heure_debut, Timetable.heure_fin <= heure_fin)
            )
        )
        
        if exclude_id:
            query = query.filter(Timetable.id != exclude_id)
            
        return query.all()
    
    def check_room_conflict(
        self,
        db: Session,
        *,
        salle: str,
        jour_semaine: str,
        heure_debut: time,
        heure_fin: time,
        exclude_id: Optional[int] = None
    ) -> List[Timetable]:
        """Check for room scheduling conflicts"""
        if not salle:
            return []
            
        query = db.query(Timetable).filter(
            Timetable.salle == salle,
            Timetable.jour_semaine == jour_semaine,
            Timetable.is_active == True,
            or_(
                and_(Timetable.heure_debut <= heure_debut, Timetable.heure_fin > heure_debut),
                and_(Timetable.heure_debut < heure_fin, Timetable.heure_fin >= heure_fin),
                and_(Timetable.heure_debut >= heure_debut, Timetable.heure_fin <= heure_fin)
            )
        )
        
        if exclude_id:
            query = query.filter(Timetable.id != exclude_id)
            
        return query.all()
    
    def get_weekly_schedule(
        self, 
        db: Session, 
        *, 
        classe_id: Optional[int] = None,
        enseignant_id: Optional[int] = None,
        annee_scolaire_id: Optional[int] = None
    ) -> Dict[str, List[Timetable]]:
        """Get weekly schedule organized by day"""
        query = db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher)
        ).filter(Timetable.is_active == True)
        
        if classe_id:
            query = query.filter(Timetable.classe_id == classe_id)
        if enseignant_id:
            query = query.filter(Timetable.enseignant_id == enseignant_id)
        if annee_scolaire_id:
            query = query.filter(Timetable.annee_scolaire_id == annee_scolaire_id)
        
        timetables = query.order_by(Timetable.jour_semaine, Timetable.heure_debut).all()
        
        # Organize by day
        days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        weekly_schedule = {day: [] for day in days}
        for timetable in timetables:
            if timetable.jour_semaine in weekly_schedule:
                weekly_schedule[timetable.jour_semaine].append(timetable)
        
        return weekly_schedule
    
    def search_timetables(
        self,
        db: Session,
        *,
        classe_id: Optional[int] = None,
        enseignant_id: Optional[int] = None,
        matiere_id: Optional[int] = None,
        annee_scolaire_id: Optional[int] = None,
        jour_semaine: Optional[str] = None,
        salle: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Timetable]:
        """Advanced search for timetables with multiple filters"""
        query = db.query(Timetable).options(
            joinedload(Timetable.classe),
            joinedload(Timetable.subject),
            joinedload(Timetable.teacher),
            joinedload(Timetable.school_year)
        ).filter(Timetable.is_active == True)
        
        if classe_id:
            query = query.filter(Timetable.classe_id == classe_id)
        if enseignant_id:
            query = query.filter(Timetable.enseignant_id == enseignant_id)
        if matiere_id:
            query = query.filter(Timetable.matiere_id == matiere_id)
        if annee_scolaire_id:
            query = query.filter(Timetable.annee_scolaire_id == annee_scolaire_id)
        if jour_semaine:
            query = query.filter(Timetable.jour_semaine == jour_semaine)
        if salle:
            query = query.filter(Timetable.salle.ilike(f"%{salle}%"))
        
        return query.order_by(
            Timetable.jour_semaine, 
            Timetable.heure_debut
        ).offset(skip).limit(limit).all()

timetable = CRUDTimetable(Timetable)
