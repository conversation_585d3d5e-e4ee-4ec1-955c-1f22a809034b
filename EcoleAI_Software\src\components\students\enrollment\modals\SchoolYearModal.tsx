import React, { useState } from 'react';
import { X, Calendar, AlertCircle } from 'lucide-react';
import { apiClient } from '../../../../config/api';

interface SchoolYearModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (schoolYear: any) => void;
}

interface FormData {
  annee: string;
  date_debut: string;
  date_fin: string;
  is_active: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export default function SchoolYearModal({ isOpen, onClose, onSuccess }: SchoolYearModalProps) {
  const [formData, setFormData] = useState<FormData>({
    annee: '',
    date_debut: '',
    date_fin: '',
    is_active: true
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.annee.trim()) {
      errors.annee = 'L\'année scolaire est requise';
    } else if (!/^\d{4}-\d{4}$/.test(formData.annee)) {
      errors.annee = 'Format requis: YYYY-YYYY (ex: 2024-2025)';
    }

    if (!formData.date_debut) {
      errors.date_debut = 'La date de début est requise';
    }

    if (!formData.date_fin) {
      errors.date_fin = 'La date de fin est requise';
    }

    if (formData.date_debut && formData.date_fin && formData.date_debut >= formData.date_fin) {
      errors.date_fin = 'La date de fin doit être après la date de début';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const response = await apiClient.post('/api/school-years/', formData);
      onSuccess(response.data);
      onClose();
      resetForm();
    } catch (error: any) {
      console.error('Error creating school year:', error);
      
      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        const newFormErrors: FormErrors = {};
        
        if (backendErrors.annee) {
          newFormErrors.annee = Array.isArray(backendErrors.annee) ? backendErrors.annee[0] : backendErrors.annee;
        }
        if (backendErrors.date_debut) {
          newFormErrors.date_debut = Array.isArray(backendErrors.date_debut) ? backendErrors.date_debut[0] : backendErrors.date_debut;
        }
        if (backendErrors.date_fin) {
          newFormErrors.date_fin = Array.isArray(backendErrors.date_fin) ? backendErrors.date_fin[0] : backendErrors.date_fin;
        }
        
        setFormErrors(newFormErrors);
      } else {
        setFormErrors({ general: 'Erreur lors de la création de l\'année scolaire' });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      annee: '',
      date_debut: '',
      date_fin: '',
      is_active: true
    });
    setFormErrors({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-1">
              Nouvelle année scolaire
            </h2>
            <p className="text-sm text-gray-600">
              Créez une nouvelle année scolaire
            </p>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
            type="button"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {formErrors.general && (
            <div className="flex items-center p-3 text-red-700 bg-red-100 border border-red-300 rounded-md">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span className="text-sm">{formErrors.general}</span>
            </div>
          )}

          {/* Année */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Année scolaire *
            </label>
            <input
              type="text"
              value={formData.annee}
              onChange={(e) => setFormData({ ...formData, annee: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.annee 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="2024-2025"
            />
            {formErrors.annee && (
              <p className="mt-1 text-sm text-red-600">{formErrors.annee}</p>
            )}
          </div>

          {/* Date début */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date de début *
            </label>
            <input
              type="date"
              value={formData.date_debut}
              onChange={(e) => setFormData({ ...formData, date_debut: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.date_debut 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
            />
            {formErrors.date_debut && (
              <p className="mt-1 text-sm text-red-600">{formErrors.date_debut}</p>
            )}
          </div>

          {/* Date fin */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date de fin *
            </label>
            <input
              type="date"
              value={formData.date_fin}
              onChange={(e) => setFormData({ ...formData, date_fin: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.date_fin 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
            />
            {formErrors.date_fin && (
              <p className="mt-1 text-sm text-red-600">{formErrors.date_fin}</p>
            )}
          </div>

          {/* Active */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Année active</span>
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200"
              disabled={isLoading}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 hover:opacity-90 disabled:opacity-50"
              style={{ backgroundColor: '#0a1186' }}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </div>
              ) : (
                'Créer'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
