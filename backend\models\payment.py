# models/payment.py
from sqlalchemy import Column, Integer, String, Float, ForeignKey, DateTime, Date
from sqlalchemy.orm import relationship
from datetime import datetime, date
from db import Base

class Payment(Base):
    __tablename__ = "payments"

    id = Column(Integer, primary_key=True, index=True)
    eleve_id = Column(Integer, ForeignKey("students.id"), nullable=False)
    montant = Column(Float, nullable=False)  # Payment amount
    motif = Column(String, nullable=False)  # registration, monthly fee, cafeteria, etc.
    date_paiement = Column(Date, nullable=False, default=date.today)
    statut = Column(String, nullable=False, default="paid")  # paid, partial, pending
    methode_paiement = Column(String, nullable=True)  # cash, bank transfer, etc.
    reference = Column(String, nullable=True)  # Payment reference number
    commentaire = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    student = relationship("Student", back_populates="payments")
