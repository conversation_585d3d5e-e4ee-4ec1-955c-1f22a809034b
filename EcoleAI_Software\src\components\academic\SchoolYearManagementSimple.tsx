import React, { useState, useEffect } from 'react';
import { Calendar, Plus, Search, Filter, Edit, Eye, CheckCircle, XCircle, Loader, Trash2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { apiClient } from '../../config/api';

interface SchoolYear {
  id: number;
  annee: string;  // Backend uses 'annee' not 'nom'
  date_debut: string;
  date_fin: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface SchoolYearFormData {
  annee: string;  // Backend expects 'annee' not 'nom'
  date_debut: string;
  date_fin: string;
  is_active: boolean;
}

export default function SchoolYearManagementSimple() {
  const [schoolYears, setSchoolYears] = useState<SchoolYear[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [showModal, setShowModal] = useState(false);
  const [editingSchoolYear, setEditingSchoolYear] = useState<SchoolYear | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formData, setFormData] = useState<SchoolYearFormData>({
    annee: '',
    date_debut: '',
    date_fin: '',
    is_active: true
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const { user } = useAuth();

  useEffect(() => {
    loadSchoolYears();
  }, []);

  const loadSchoolYears = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await apiClient.get('/api/school-years/');
      setSchoolYears(response.data);
    } catch (error) {
      console.error('Error loading school years:', error);
      setError('Erreur lors du chargement des années scolaires');
    } finally {
      setLoading(false);
    }
  };

  const filteredSchoolYears = schoolYears.filter(schoolYear => {
    const matchesSearch = searchTerm === '' ||
      schoolYear.annee.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'active' && schoolYear.is_active) ||
      (statusFilter === 'inactive' && !schoolYear.is_active);

    return matchesSearch && matchesStatus;
  });

  const canEdit = user?.role === 'admin';

  const resetForm = () => {
    setFormData({
      annee: '',
      date_debut: '',
      date_fin: '',
      is_active: true
    });
    setFormErrors({});
  };

  const handleAddNew = () => {
    if (!canEdit) return;
    setEditingSchoolYear(null);
    resetForm();
    setShowModal(true);
  };

  const handleEdit = (schoolYear: SchoolYear) => {
    if (!canEdit) return;
    setEditingSchoolYear(schoolYear);
    setFormData({
      annee: schoolYear.annee,
      date_debut: schoolYear.date_debut,
      date_fin: schoolYear.date_fin,
      is_active: schoolYear.is_active
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleView = (schoolYear: SchoolYear) => {
    console.log(`Voir la fiche de l'année scolaire ${schoolYear.annee}`);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.annee.trim()) {
      errors.annee = 'Le nom de l\'année scolaire est requis';
    } else {
      // Validate format like "2024-2025"
      const yearPattern = /^\d{4}-\d{4}$/;
      if (!yearPattern.test(formData.annee)) {
        errors.annee = 'Format requis: YYYY-YYYY (ex: 2024-2025)';
      } else {
        const [startYear, endYear] = formData.annee.split('-').map(Number);
        if (endYear !== startYear + 1) {
          errors.annee = 'L\'année de fin doit être l\'année de début + 1';
        }
      }
    }
    if (!formData.date_debut) {
      errors.date_debut = 'La date de début est requise';
    }
    if (!formData.date_fin) {
      errors.date_fin = 'La date de fin est requise';
    }
    if (formData.date_debut && formData.date_fin && formData.date_debut >= formData.date_fin) {
      errors.date_fin = 'La date de fin doit être postérieure à la date de début';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setFormLoading(true);
    try {
      console.log('Submitting school year data:', formData);

      if (editingSchoolYear) {
        await apiClient.put(`/api/school-years/${editingSchoolYear.id}/`, formData);
      } else {
        await apiClient.post('/api/school-years/', formData);
      }

      setShowModal(false);
      loadSchoolYears();
      resetForm();
    } catch (error: any) {
      console.error('Error saving school year:', error);

      // Handle validation errors from backend
      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        console.log('Backend validation errors:', backendErrors);

        // Map backend errors to form errors
        const newFormErrors: Record<string, string> = {};

        if (backendErrors.annee) {
          newFormErrors.annee = Array.isArray(backendErrors.annee) ? backendErrors.annee[0] : backendErrors.annee;
        }
        if (backendErrors.date_debut) {
          newFormErrors.date_debut = Array.isArray(backendErrors.date_debut) ? backendErrors.date_debut[0] : backendErrors.date_debut;
        }
        if (backendErrors.date_fin) {
          newFormErrors.date_fin = Array.isArray(backendErrors.date_fin) ? backendErrors.date_fin[0] : backendErrors.date_fin;
        }
        if (backendErrors.is_active) {
          newFormErrors.is_active = Array.isArray(backendErrors.is_active) ? backendErrors.is_active[0] : backendErrors.is_active;
        }

        // If we have specific field errors, show them
        if (Object.keys(newFormErrors).length > 0) {
          setFormErrors(newFormErrors);
        } else {
          // Generic validation error
          setError(`Erreur de validation: ${JSON.stringify(backendErrors)}`);
        }
      } else {
        setError('Erreur lors de la sauvegarde');
      }
    } finally {
      setFormLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!canEdit || !confirm('Êtes-vous sûr de vouloir supprimer cette année scolaire ?')) return;

    try {
      await apiClient.delete(`/api/school-years/${id}/`);
      loadSchoolYears();
    } catch (error) {
      console.error('Error deleting school year:', error);
      setError('Erreur lors de la suppression');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Années Scolaires</h1>
          <p className="text-gray-600 mt-1">
            {canEdit ? 'Management des périodes académiques' : 'Consultation des périodes académiques'}
          </p>
        </div>
        {canEdit && (
          <button
            onClick={handleAddNew}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
            style={{ backgroundColor: '#0a1186' }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle Année Scolaire
          </button>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={loadSchoolYears}
            className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
          >
            Réessayer
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-3 text-gray-600">Chargement des années scolaires...</span>
        </div>
      ) : (
        <>
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Années</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{schoolYears.length}</p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
                  <Calendar className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Année Active</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {schoolYears.filter(sy => sy.is_active).length}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
                  <CheckCircle className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Années Archivées</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {schoolYears.filter(sy => !sy.is_active).length}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
                  <XCircle className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Année Courante</p>
                  <p className="text-lg font-bold text-gray-900 mt-1">
                    {schoolYears.find(sy => sy.is_active)?.annee || 'Aucune'}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
                  <Calendar className="h-6 w-6 text-green-700" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher une année scolaire..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <select
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
              >
                <option value="all">Tous les statuts</option>
                <option value="active">Actives</option>
                <option value="inactive">Archivées</option>
              </select>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Filter className="h-4 w-4 mr-2" />
                Plus de filtres
              </button>
            </div>
          </div>
          {/* School Years Table */}
          {filteredSchoolYears.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-12 text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune année scolaire trouvée</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all'
                  ? 'Aucune année scolaire ne correspond aux critères de recherche.'
                  : 'Aucune année scolaire n\'est encore configurée.'
                }
              </p>
              {canEdit && !searchTerm && statusFilter === 'all' && (
                <button
                  onClick={handleAddNew}
                  className="inline-flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter la première année scolaire
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead style={{ backgroundColor: '#0a1186' }}>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Année Scolaire
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Période
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Durée
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSchoolYears.map((schoolYear) => (
                      <tr key={schoolYear.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <Calendar className="h-5 w-5 text-gray-600" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {schoolYear.annee}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {schoolYear.id}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            Du {formatDate(schoolYear.date_debut)}
                          </div>
                          <div className="text-sm text-gray-500">
                            Au {formatDate(schoolYear.date_fin)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {Math.ceil((new Date(schoolYear.date_fin).getTime() - new Date(schoolYear.date_debut).getTime()) / (1000 * 60 * 60 * 24))} jours
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${schoolYear.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                            }`}>
                            {schoolYear.is_active ? 'Active' : 'Archivée'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleView(schoolYear)}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            {canEdit && (
                              <>
                                <button
                                  onClick={() => handleEdit(schoolYear)}
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete(schoolYear.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Modal Form */}
          {showModal && canEdit && (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    {editingSchoolYear ? 'Modifier l\'année scolaire' : 'Nouvelle année scolaire'}
                  </h2>
                  <button
                    onClick={() => setShowModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="text-2xl">&times;</span>
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Nom de l'année scolaire */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Année scolaire *
                    </label>
                    <input
                      type="text"
                      value={formData.annee}
                      onChange={(e) => setFormData({ ...formData, annee: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.annee ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="Ex: 2024-2025"
                    />
                    {formErrors.annee && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.annee}</p>
                    )}
                    <p className="mt-1 text-sm text-gray-500">
                      Format requis: YYYY-YYYY (ex: 2024-2025)
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Date de début */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date de début *
                      </label>
                      <input
                        type="date"
                        value={formData.date_debut}
                        onChange={(e) => setFormData({ ...formData, date_debut: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.date_debut ? 'border-red-500' : 'border-gray-300'
                          }`}
                      />
                      {formErrors.date_debut && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.date_debut}</p>
                      )}
                    </div>

                    {/* Date de fin */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date de fin *
                      </label>
                      <input
                        type="date"
                        value={formData.date_fin}
                        onChange={(e) => setFormData({ ...formData, date_fin: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.date_fin ? 'border-red-500' : 'border-gray-300'
                          }`}
                      />
                      {formErrors.date_fin && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.date_fin}</p>
                      )}
                    </div>
                  </div>

                  {/* Statut */}
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Année scolaire active</span>
                    </label>
                    {formErrors.is_active && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.is_active}</p>
                    )}
                    <p className="mt-1 text-sm text-gray-500">
                      Une seule année scolaire peut être active à la fois
                    </p>
                  </div>

                  <div className="flex justify-end space-x-3 pt-6">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                      disabled={formLoading}
                    >
                      Annuler
                    </button>
                    <button
                      type="submit"
                      disabled={formLoading}
                      className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 disabled:opacity-50"
                      style={{ backgroundColor: '#0a1186' }}
                    >
                      {formLoading ? 'Enregistrement...' : (editingSchoolYear ? 'Modifier' : 'Ajouter')}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
