import React, { useState } from 'react';
import { Bell, CheckCircle, AlertTriangle, Info, Calendar, CreditCard, Award, Users, Search, Filter } from 'lucide-react';
import { useNotifications } from '../contexts/NotificationContext';

const notificationIcons = {
  absence: AlertTriangle,
  payment: CreditCard,
  result: Award,
  meeting: Calendar,
  info: Info
};

const notificationColors = {
  absence: 'text-orange-600',
  payment: 'text-blue-600', 
  result: 'text-green-600',
  meeting: 'text-purple-600',
  info: 'text-gray-600'
};

const notificationBgColors = {
  absence: 'bg-orange-100',
  payment: 'bg-blue-100',
  result: 'bg-green-100', 
  meeting: 'bg-purple-100',
  info: 'bg-gray-100'
};

export default function Notifications() {
  const { notifications, markAsRead, markAllAsRead } = useNotifications();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);

  const notificationTypes = [
    { value: 'absence', label: 'Absences' },
    { value: 'payment', label: 'Paiements' },
    { value: 'result', label: 'Résultats' },
    { value: 'meeting', label: 'Réunions' }
  ];

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === '' || notification.type === selectedType;
    const matchesReadStatus = !showUnreadOnly || !notification.read;
    return matchesSearch && matchesType && matchesReadStatus;
  });

  const unreadCount = notifications.filter(n => !n.read).length;
  const todayNotifications = notifications.filter(n => {
    const today = new Date().toDateString();
    return n.timestamp.toDateString() === today;
  }).length;

  const formatTime = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 60) {
      return `Il y a ${minutes} min`;
    } else if (hours < 24) {
      return `Il y a ${hours}h`;
    } else {
      return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Centre de Notifications</h1>
          <p className="text-gray-600 mt-1">Alertes et notifications du système</p>
        </div>
        {unreadCount > 0 && (
          <button
            onClick={markAllAsRead}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
            style={{ backgroundColor: '#0a1186' }}
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Marquer tout comme lu
          </button>
        )}
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Notifications</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{notifications.length}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <Bell className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Non Lues</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{unreadCount}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <AlertTriangle className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Aujourd'hui</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{todayNotifications}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <Calendar className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critiques</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {notifications.filter(n => n.type === 'absence').length}
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <Users className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher dans les notifications..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
          >
            <option value="">Tous les types</option>
            {notificationTypes.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="unread-only"
              checked={showUnreadOnly}
              onChange={(e) => setShowUnreadOnly(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="unread-only" className="ml-2 text-sm font-medium text-gray-700">
              Non lues uniquement
            </label>
          </div>
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <Filter className="h-4 w-4 mr-2" />
            Plus de filtres
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {filteredNotifications.length === 0 ? (
          <div className="bg-white rounded-lg p-8 shadow-sm border border-gray-100 text-center">
            <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune notification</h3>
            <p className="text-gray-600">
              {searchTerm || selectedType || showUnreadOnly
                ? 'Aucune notification ne correspond à vos critères de recherche.'
                : 'Vous n\'avez aucune notification pour le moment.'}
            </p>
          </div>
        ) : (
          filteredNotifications.map((notification) => {
            const IconComponent = notificationIcons[notification.type];
            const iconColor = notificationColors[notification.type];
            const bgColor = notificationBgColors[notification.type];

            return (
              <div
                key={notification.id}
                className={`bg-white rounded-lg p-6 shadow-sm border border-gray-100 transition-all hover:shadow-md cursor-pointer ${
                  !notification.read ? 'border-l-4 border-l-blue-500' : ''
                }`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="flex items-start space-x-4">
                  <div className={`p-2 rounded-lg ${bgColor}`}>
                    <IconComponent className={`h-5 w-5 ${iconColor}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className={`text-sm font-medium ${
                        !notification.read ? 'text-gray-900' : 'text-gray-700'
                      }`}>
                        {notification.title}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          {formatTime(notification.timestamp)}
                        </span>
                        {!notification.read && (
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        )}
                      </div>
                    </div>
                    <p className={`mt-1 text-sm ${
                      !notification.read ? 'text-gray-700' : 'text-gray-500'
                    }`}>
                      {notification.message}
                    </p>
                    <div className="mt-2 flex items-center space-x-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${iconColor}`}>
                        {notificationTypes.find(t => t.value === notification.type)?.label}
                      </span>
                      <span className="text-xs text-gray-400">
                        {notification.timestamp.toLocaleDateString('fr-FR', {
                          day: 'numeric',
                          month: 'long',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Actions Rapides</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setSelectedType('absence')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
          >
            <AlertTriangle className="h-8 w-8 text-orange-600 mb-2" />
            <p className="font-medium text-gray-900">Voir Absences</p>
            <p className="text-sm text-gray-500">
              {notifications.filter(n => n.type === 'absence').length} notifications
            </p>
          </button>
          
          <button
            onClick={() => setSelectedType('payment')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
          >
            <CreditCard className="h-8 w-8 text-blue-600 mb-2" />
            <p className="font-medium text-gray-900">Voir Paiements</p>
            <p className="text-sm text-gray-500">
              {notifications.filter(n => n.type === 'payment').length} notifications
            </p>
          </button>
          
          <button
            onClick={() => setSelectedType('result')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
          >
            <Award className="h-8 w-8 text-green-600 mb-2" />
            <p className="font-medium text-gray-900">Voir Résultats</p>
            <p className="text-sm text-gray-500">
              {notifications.filter(n => n.type === 'result').length} notifications
            </p>
          </button>
          
          <button
            onClick={() => setSelectedType('meeting')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
          >
            <Calendar className="h-8 w-8 text-purple-600 mb-2" />
            <p className="font-medium text-gray-900">Voir Réunions</p>
            <p className="text-sm text-gray-500">
              {notifications.filter(n => n.type === 'meeting').length} notifications
            </p>
          </button>
        </div>
      </div>
    </div>
  );
}