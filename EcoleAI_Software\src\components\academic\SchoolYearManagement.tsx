import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Building,
  BookOpen,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Users,
  GraduationCap,
  CheckCircle,
  XCircle,
  Loader
} from 'lucide-react';
import { apiClient } from '../../config/api';

// TypeScript Interfaces
interface SchoolYear {
  id: number;
  nom: string;
  date_debut: string;
  date_fin: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface Class {
  id: number;
  nom: string;
  niveau: string;
  capacite: number;
  annee_scolaire_id: number;
  professeur_principal_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface Subject {
  id: number;
  nom: string;
  description?: string;
  coefficient: number;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

type TabType = 'school-years' | 'classes' | 'subjects';

export default function SchoolYearManagement() {
  const [activeTab, setActiveTab] = useState<TabType>('school-years');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  // Data states
  const [schoolYears, setSchoolYears] = useState<SchoolYear[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    setLoading(true);
    setError('');
    try {
      switch (activeTab) {
        case 'school-years':
          const schoolYearsResponse = await apiClient.get('/api/school-years/');
          setSchoolYears(schoolYearsResponse.data);
          break;
        case 'classes':
          const classesResponse = await apiClient.get('/api/classes/');
          setClasses(classesResponse.data);
          break;
        case 'subjects':
          const subjectsResponse = await apiClient.get('/api/subjects/');
          setSubjects(subjectsResponse.data);
          break;
      }
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingItem(null);
    setShowModal(true);
  };

  const handleEdit = (item: any) => {
    setEditingItem(item);
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) return;

    try {
      const endpoint = activeTab === 'school-years' ? '/api/school-years/' :
        activeTab === 'classes' ? '/api/classes/' : '/api/subjects/';
      await apiClient.delete(`${endpoint}${id}/`);
      loadData();
    } catch (error) {
      console.error('Error deleting item:', error);
      setError('Erreur lors de la suppression');
    }
  };

  const tabs = [
    { id: 'school-years', name: 'Années Scolaires', icon: Calendar },
    { id: 'classes', name: 'Classes', icon: Building },
    { id: 'subjects', name: 'Matières', icon: BookOpen }
  ];

  const getFilteredData = () => {
    const data = activeTab === 'school-years' ? schoolYears :
      activeTab === 'classes' ? classes : subjects;

    if (!searchTerm) return data;

    return data.filter(item =>
      item.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (activeTab === 'classes' && (item as Class).niveau?.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (activeTab === 'subjects' && (item as Subject).description?.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const getTabTitle = () => {
    switch (activeTab) {
      case 'school-years': return 'Gestion des Années Scolaires';
      case 'classes': return 'Gestion des Classes';
      case 'subjects': return 'Gestion des Matières';
      default: return 'Gestion Académique';
    }
  };

  const getTabDescription = () => {
    switch (activeTab) {
      case 'school-years': return 'Configurez les périodes académiques et leur statut';
      case 'classes': return 'Organisez les classes par niveau et capacité';
      case 'subjects': return 'Définissez les matières et leurs coefficients';
      default: return 'Configuration du système académique';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Gestion Académique</h1>
          <p className="text-gray-600 mt-1">Configuration des années scolaires, classes et matières</p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as TabType)}
                    className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${isActive
                      ? 'text-white border-current'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    style={isActive ? { color: '#0a1186', borderColor: '#0a1186' } : {}}
                  >
                    <Icon className="h-5 w-5 mr-2" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{getTabTitle()}</h2>
              <p className="text-gray-600 mt-1">{getTabDescription()}</p>
            </div>
            <button
              onClick={handleAdd}
              className="flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-opacity shadow-sm"
              style={{ backgroundColor: '#0a1186' }}
            >
              <Plus className="h-5 w-5 mr-2" />
              Ajouter
            </button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
            <button
              onClick={loadData}
              className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
            >
              Réessayer
            </button>
          </div>
        )}

        {/* Search and Filters */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={`Rechercher ${activeTab === 'school-years' ? 'une année' : activeTab === 'classes' ? 'une classe' : 'une matière'}...`}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500">
                {getFilteredData().length} élément(s) trouvé(s)
              </span>
            </div>
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-3 text-gray-600">Chargement...</span>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            {getFilteredData().length === 0 ? (
              <div className="text-center py-12">
                <div className="h-12 w-12 text-gray-400 mx-auto mb-4">
                  {activeTab === 'school-years' && <Calendar className="h-12 w-12" />}
                  {activeTab === 'classes' && <Building className="h-12 w-12" />}
                  {activeTab === 'subjects' && <BookOpen className="h-12 w-12" />}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun élément trouvé
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm
                    ? 'Aucun élément ne correspond aux critères de recherche.'
                    : `Aucun ${activeTab === 'school-years' ? 'année scolaire' : activeTab === 'classes' ? 'classe' : 'matière'} n'est encore configuré(e).`
                  }
                </p>
                <button
                  onClick={handleAdd}
                  className="inline-flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter le premier élément
                </button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead style={{ backgroundColor: '#0a1186' }}>
                    <tr>
                      {activeTab === 'school-years' && (
                        <>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Année Scolaire
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Période
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Statut
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            Actions
                          </th>
                        </>
                      )}
                      {activeTab === 'classes' && (
                        <>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Classe
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Niveau
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Capacité
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Statut
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            Actions
                          </th>
                        </>
                      )}
                      {activeTab === 'subjects' && (
                        <>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Matière
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Description
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Coefficient
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Statut
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            Actions
                          </th>
                        </>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getFilteredData().map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        {activeTab === 'school-years' && (
                          <>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <Calendar className="h-8 w-8 text-gray-400 mr-3" />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {(item as SchoolYear).nom}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    ID: {item.id}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {new Date((item as SchoolYear).date_debut).toLocaleDateString('fr-FR')} - {new Date((item as SchoolYear).date_fin).toLocaleDateString('fr-FR')}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(item as SchoolYear).is_active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                                }`}>
                                {(item as SchoolYear).is_active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleEdit(item)}
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete(item.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </>
                        )}
                        {activeTab === 'classes' && (
                          <>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <Building className="h-8 w-8 text-gray-400 mr-3" />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {(item as Class).nom}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    ID: {item.id}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {(item as Class).niveau}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {(item as Class).capacite} élèves
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(item as Class).is_active
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                                }`}>
                                {(item as Class).is_active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleEdit(item)}
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete(item.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </>
                        )}
                        {activeTab === 'subjects' && (
                          <>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <BookOpen className="h-8 w-8 text-gray-400 mr-3" />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {(item as Subject).nom}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    ID: {item.id}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {(item as Subject).description || 'Aucune description'}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {(item as Subject).coefficient}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(item as Subject).is_active
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                                }`}>
                                {(item as Subject).is_active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleEdit(item)}
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete(item.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
