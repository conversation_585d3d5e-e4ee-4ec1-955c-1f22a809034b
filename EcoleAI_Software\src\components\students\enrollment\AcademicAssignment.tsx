import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  GraduationCap,
  BookOpen,
  User<PERSON>heck,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Calendar
} from 'lucide-react';
import { apiClient } from '../../../config/api';

interface AcademicAssignmentProps {
  enrollmentData: any;
  updateEnrollmentData: (section: string, data: any) => void;
  updateStepCompletion: (stepId: string, completed: boolean) => void;
  currentStep: any;
}

interface Course {
  id: number;
  enseignant_id: number;
  matiere_id: number;
  classe_id: number;
  horaire?: string;
  teacher?: any;
  subject?: any;
}

interface Subject {
  id: number;
  nom: string;
  coef: number;
  description?: string;
}

interface Teacher {
  id: number;
  nom: string;
  prenom: string;
  specialite?: string;
  email?: string;
  is_active: boolean;
}

export default function AcademicAssignment({
  enrollmentData,
  updateEnrollmentData,
  updateStepCompletion,
  currentStep
}: AcademicAssignmentProps) {
  const [selectedClass, setSelectedClass] = useState<any>(null);
  const [availableCourses, setAvailableCourses] = useState<Course[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [loadedClassId, setLoadedClassId] = useState<number | null>(null);

  useEffect(() => {
    // Load subjects and teachers from prerequisites (if available)
    if (enrollmentData.prerequisites?.subjects && subjects.length === 0) {
      setSubjects(enrollmentData.prerequisites.subjects);
    }
    if (enrollmentData.prerequisites?.teachers && teachers.length === 0) {
      setTeachers(enrollmentData.prerequisites.teachers);
    }
  }, [enrollmentData.prerequisites?.subjects, enrollmentData.prerequisites?.teachers, subjects.length, teachers.length]);

  // Define loadAcademicData function first
  const loadAcademicData = useCallback(async (classId: number) => {
    // Prevent loading if already loading or if we already have data for this class
    if (loading || loadedClassId === classId) return;

    setLoading(true);
    setError('');

    try {
      // Note: The backend doesn't have a direct courses endpoint for a class
      // This would need to be implemented in the backend
      // For now, we'll simulate the course assignment based on subjects and teachers

      let allSubjects = subjects;
      let allTeachers = teachers;

      // Only fetch subjects if we don't have them yet
      if (subjects.length === 0) {
        const subjectsResponse = await apiClient.get('/api/subjects/');
        allSubjects = subjectsResponse.data;
        setSubjects(allSubjects);
      }

      // Only fetch teachers if we don't have them yet
      if (teachers.length === 0) {
        const teachersResponse = await apiClient.get('/api/teachers/?active_only=true');
        allTeachers = teachersResponse.data;
        setTeachers(allTeachers);
      }

      // Simulate course assignments (this would come from a real courses API)
      const simulatedCourses = allSubjects.map((subject: Subject, index: number) => {
        // Try to find a teacher with matching specialty
        const matchingTeacher = allTeachers.find((teacher: Teacher) =>
          teacher.specialite?.toLowerCase().includes(subject.nom.toLowerCase())
        );

        // If no matching teacher, assign the first available teacher
        const assignedTeacher = matchingTeacher || allTeachers[index % allTeachers.length];

        return {
          id: index + 1,
          enseignant_id: assignedTeacher?.id || 0,
          matiere_id: subject.id,
          classe_id: classId,
          horaire: `${8 + index}h00 - ${9 + index}h00`, // Simulate schedule
          teacher: assignedTeacher,
          subject: subject,
        };
      });

      setAvailableCourses(simulatedCourses);
      setLoadedClassId(classId);

    } catch (error) {
      console.error('Error loading academic data:', error);
      setError('Erreur lors du chargement des données académiques');
    } finally {
      setLoading(false);
    }
  }, [loading, loadedClassId, subjects, teachers]);

  useEffect(() => {
    // Load selected class from student data (only when class ID changes)
    if (enrollmentData.student?.classe_id && enrollmentData.prerequisites?.availableClasses) {
      const classId = parseInt(enrollmentData.student.classe_id);
      const foundClass = enrollmentData.prerequisites.availableClasses.find(
        (cls: any) => cls.id === classId
      );
      if (foundClass && (!selectedClass || selectedClass.id !== classId)) {
        setSelectedClass(foundClass);
        // Only load academic data if we haven't loaded it for this class yet
        if (loadedClassId !== classId) {
          loadAcademicData(classId);
        }
      }
    }
  }, [enrollmentData.student?.classe_id, enrollmentData.prerequisites?.availableClasses, selectedClass, loadedClassId, loadAcademicData]);

  // Define memoized functions
  const getAssignedTeachers = useMemo(() => {
    const teacherIds = availableCourses.map(course => course.enseignant_id);
    return teachers.filter(teacher => teacherIds.includes(teacher.id));
  }, [availableCourses, teachers]);

  const getSubjectById = useCallback((subjectId: number) => {
    return subjects.find(subject => subject.id === subjectId);
  }, [subjects]);

  const getTeacherById = useCallback((teacherId: number) => {
    return teachers.find(teacher => teacher.id === teacherId);
  }, [teachers]);

  const getTotalCoefficients = useMemo(() => {
    return availableCourses.reduce((total, course) => {
      const subject = getSubjectById(course.matiere_id);
      return total + (subject?.coef || 1);
    }, 0);
  }, [availableCourses, getSubjectById]);

  useEffect(() => {
    // Update step completion
    const isComplete = selectedClass && availableCourses.length > 0;
    updateStepCompletion(currentStep.id, isComplete);

    // Update enrollment data
    updateEnrollmentData('academic', {
      selectedClass,
      assignedCourses: availableCourses,
      assignedTeachers: getAssignedTeachers,
    });
  }, [selectedClass, availableCourses, getAssignedTeachers]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des données académiques...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
          <div>
            <h4 className="font-medium text-red-900">Erreur</h4>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedClass) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-yellow-500 mr-3" />
          <div>
            <h4 className="font-medium text-yellow-900">Classe Non Sélectionnée</h4>
            <p className="text-sm text-yellow-700">
              Veuillez d'abord sélectionner une classe dans l'étape précédente.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Affectation Académique</h2>
        <p className="text-sm text-gray-600 mt-1">
          Révision des cours et professeurs assignés à l'élève
        </p>
      </div>

      {/* Selected Class Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-4 flex items-center">
          <GraduationCap className="h-5 w-5 mr-2" />
          Classe Sélectionnée
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-blue-800">Nom de la Classe</label>
            <p className="text-lg font-semibold text-blue-900">{selectedClass.nom}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-blue-800">Niveau</label>
            <p className="text-lg font-semibold text-blue-900">{selectedClass.niveau}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-blue-800">Année Scolaire</label>
            <p className="text-lg font-semibold text-blue-900">
              {enrollmentData.prerequisites?.activeSchoolYear?.annee || 'Non définie'}
            </p>
          </div>
        </div>
      </div>

      {/* Course Assignments */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <BookOpen className="h-5 w-5 mr-2 text-green-600" />
          Cours Assignés
        </h3>

        {availableCourses.length === 0 ? (
          <p className="text-gray-500 text-center py-4">
            Aucun cours configuré pour cette classe.
          </p>
        ) : (
          <div className="space-y-4">
            {availableCourses.map((course) => {
              const subject = getSubjectById(course.matiere_id);
              const teacher = getTeacherById(course.enseignant_id);

              return (
                <div key={course.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Matière</label>
                      <p className="font-semibold text-gray-900">{subject?.nom || 'N/A'}</p>
                      <p className="text-sm text-gray-600">
                        Coefficient: {subject?.coef || 1}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Professeur</label>
                      <p className="font-semibold text-gray-900">
                        {teacher ? `${teacher.prenom} ${teacher.nom}` : 'Non assigné'}
                      </p>
                      {teacher?.specialite && (
                        <p className="text-sm text-gray-600">
                          Spécialité: {teacher.specialite}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Horaire</label>
                      <p className="font-semibold text-gray-900 flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {course.horaire || 'À définir'}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Statut</label>
                      <div className="flex items-center">
                        {teacher ? (
                          <>
                            <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                            <span className="text-sm text-green-700">Assigné</span>
                          </>
                        ) : (
                          <>
                            <AlertCircle className="h-4 w-4 text-yellow-500 mr-1" />
                            <span className="text-sm text-yellow-700">En attente</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Teachers Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <UserCheck className="h-5 w-5 mr-2 text-purple-600" />
          Professeurs Assignés
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {getAssignedTeachers.map((teacher) => (
            <div key={teacher.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                  <UserCheck className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">
                    {teacher.prenom} {teacher.nom}
                  </p>
                  {teacher.specialite && (
                    <p className="text-sm text-gray-600">{teacher.specialite}</p>
                  )}
                  {teacher.email && (
                    <p className="text-sm text-gray-500">{teacher.email}</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Academic Summary */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-green-900 mb-4">Résumé Académique</h3>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-800">{availableCourses.length}</div>
            <div className="text-sm text-green-700">Cours Assignés</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-800">{getAssignedTeachers.length}</div>
            <div className="text-sm text-green-700">Professeurs</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-800">{getTotalCoefficients}</div>
            <div className="text-sm text-green-700">Total Coefficients</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-800">{selectedClass.effectif + 1}</div>
            <div className="text-sm text-green-700">Effectif Final</div>
          </div>
        </div>
      </div>

      {/* Completion Status */}
      {availableCourses.length > 0 && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
            <div>
              <h4 className="font-medium text-green-900">Affectation Académique Complète</h4>
              <p className="text-sm text-green-700">
                L'élève a été assigné à {availableCourses.length} cours avec {getAssignedTeachers.length} professeurs.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
