# Step 9 Implementation Guide: Advanced Authentication Features

## Analysis of Current Implementation

### ✅ Already Implemented:
1. **useTokenExpiration hook** - `src/hooks/useTokenExpiration.ts` (EXISTS)
2. **useSession hook** - `src/hooks/useSession.ts` (EXISTS)
3. **AuthService** - `src/services/authService.ts` (EXISTS)

### ❌ Issues Found & Missing Features:

1. **Token Storage Integration**: Current hooks use `localStorage.getItem('access_token')` directly instead of `SecureTokenStorage`
2. **Hook Integration**: Hooks are not being used in the main application
3. **Enhanced Session Management**: Missing advanced session features like idle timeout, session warnings
4. **Testing Utilities**: Missing authentication testing utilities
5. **Session Persistence**: Missing session restoration on page refresh
6. **Activity Tracking**: Missing user activity monitoring

## Required Implementations

### 1. Update Existing Hooks to Use SecureTokenStorage

#### File: `src/hooks/useTokenExpiration.ts`
**Action**: Replace the entire file content with:

```typescript
import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { SecureTokenStorage } from '../utils/tokenStorage';
import { envConfig } from '../config/environment';

export function useTokenExpiration() {
  const { logout } = useAuth();

  useEffect(() => {
    const checkTokenExpiration = () => {
      const token = SecureTokenStorage.getAccessToken();
      if (token) {
        try {
          // Check if token is expired using SecureTokenStorage method
          if (SecureTokenStorage.isTokenExpired(token)) {
            console.log('Token expired, logging out...');
            logout();
            return;
          }

          // Check if token is close to expiring (within threshold)
          const expiration = SecureTokenStorage.getTokenExpiration(token);
          if (expiration) {
            const timeUntilExpiry = expiration.getTime() - Date.now();
            if (timeUntilExpiry < envConfig.tokenRefreshThreshold) {
              console.log('Token close to expiry, will be refreshed by interceptor');
            }
          }
        } catch (error) {
          console.error('Error checking token expiration:', error);
          logout();
        }
      }
    };

    // Check immediately
    checkTokenExpiration();

    // Check every 5 minutes
    const interval = setInterval(checkTokenExpiration, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [logout]);
}
```

#### File: `src/hooks/useSession.ts`
**Action**: Replace the entire file content with:

```typescript
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { SecureTokenStorage } from '../utils/tokenStorage';
import { envConfig } from '../config/environment';

export interface SessionInfo {
  user: any;
  isAuthenticated: boolean;
  sessionExpiry: Date | null;
  timeUntilExpiry: number | null;
  isExpiringSoon: boolean;
  sessionDuration: number | null;
}

export function useSession(): SessionInfo {
  const { user, isAuthenticated } = useAuth();
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);
  const [sessionStart, setSessionStart] = useState<Date | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      const token = SecureTokenStorage.getAccessToken();
      if (token) {
        try {
          const expiration = SecureTokenStorage.getTokenExpiration(token);
          setSessionExpiry(expiration);
          
          // Set session start time if not already set
          if (!sessionStart) {
            setSessionStart(new Date());
          }
        } catch (error) {
          console.error('Error parsing token:', error);
          setSessionExpiry(null);
        }
      }
    } else {
      setSessionExpiry(null);
      setSessionStart(null);
    }
  }, [isAuthenticated, sessionStart]);

  const timeUntilExpiry = sessionExpiry ? sessionExpiry.getTime() - Date.now() : null;
  const isExpiringSoon = timeUntilExpiry ? timeUntilExpiry < envConfig.tokenRefreshThreshold : false;
  const sessionDuration = sessionStart ? Date.now() - sessionStart.getTime() : null;

  return {
    user,
    isAuthenticated,
    sessionExpiry,
    timeUntilExpiry,
    isExpiringSoon,
    sessionDuration
  };
}
```

### 2. Create New Advanced Hooks

#### File: `src/hooks/useIdleTimeout.ts` (NEW FILE)
**Action**: Create this new file:

```typescript
import { useEffect, useRef, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { envConfig } from '../config/environment';

interface UseIdleTimeoutOptions {
  timeout?: number; // in milliseconds
  onIdle?: () => void;
  onActive?: () => void;
  events?: string[];
}

export function useIdleTimeout(options: UseIdleTimeoutOptions = {}) {
  const { logout } = useAuth();
  const {
    timeout = envConfig.sessionTimeout,
    onIdle,
    onActive,
    events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
  } = options;

  const timeoutRef = useRef<NodeJS.Timeout>();
  const isIdleRef = useRef(false);

  const resetTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (isIdleRef.current) {
      isIdleRef.current = false;
      onActive?.();
    }

    timeoutRef.current = setTimeout(() => {
      isIdleRef.current = true;
      onIdle?.();
      
      // Auto-logout after idle timeout
      console.log('User idle timeout reached, logging out...');
      logout();
    }, timeout);
  }, [timeout, onIdle, onActive, logout]);

  useEffect(() => {
    // Set up event listeners
    events.forEach(event => {
      document.addEventListener(event, resetTimeout, true);
    });

    // Initialize timeout
    resetTimeout();

    return () => {
      // Clean up
      events.forEach(event => {
        document.removeEventListener(event, resetTimeout, true);
      });
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [events, resetTimeout]);

  return {
    isIdle: isIdleRef.current,
    resetTimeout
  };
}
```

#### File: `src/hooks/useSessionWarning.ts` (NEW FILE)
**Action**: Create this new file:

```typescript
import { useState, useEffect, useCallback } from 'react';
import { useSession } from './useSession';

interface UseSessionWarningOptions {
  warningTime?: number; // milliseconds before expiry to show warning
  onWarning?: (timeLeft: number) => void;
  onExpired?: () => void;
}

export function useSessionWarning(options: UseSessionWarningOptions = {}) {
  const { warningTime = 5 * 60 * 1000, onWarning, onExpired } = options; // 5 minutes default
  const { timeUntilExpiry, isAuthenticated } = useSession();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);

  const dismissWarning = useCallback(() => {
    setShowWarning(false);
    setTimeLeft(null);
  }, []);

  useEffect(() => {
    if (!isAuthenticated || !timeUntilExpiry) {
      setShowWarning(false);
      setTimeLeft(null);
      return;
    }

    if (timeUntilExpiry <= 0) {
      setShowWarning(false);
      setTimeLeft(null);
      onExpired?.();
      return;
    }

    if (timeUntilExpiry <= warningTime && !showWarning) {
      setShowWarning(true);
      setTimeLeft(timeUntilExpiry);
      onWarning?.(timeUntilExpiry);
    }

    if (showWarning) {
      setTimeLeft(timeUntilExpiry);
    }

    // Update every second when warning is shown
    if (showWarning) {
      const interval = setInterval(() => {
        if (timeUntilExpiry <= 0) {
          setShowWarning(false);
          setTimeLeft(null);
          onExpired?.();
        } else {
          setTimeLeft(timeUntilExpiry);
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [timeUntilExpiry, isAuthenticated, warningTime, showWarning, onWarning, onExpired]);

  return {
    showWarning,
    timeLeft,
    dismissWarning,
    formatTimeLeft: (ms: number | null) => {
      if (!ms) return '';
      const minutes = Math.floor(ms / 60000);
      const seconds = Math.floor((ms % 60000) / 1000);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  };
}
```

### 3. Create Session Warning Component

#### File: `src/components/SessionWarning.tsx` (NEW FILE)
**Action**: Create this new file:

```typescript
import React from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { useSessionWarning } from '../hooks/useSessionWarning';
import { useAuth } from '../contexts/AuthContext';

export default function SessionWarning() {
  const { logout } = useAuth();
  const { showWarning, timeLeft, dismissWarning, formatTimeLeft } = useSessionWarning({
    warningTime: 5 * 60 * 1000, // 5 minutes
    onExpired: () => {
      console.log('Session expired');
    }
  });

  if (!showWarning || !timeLeft) {
    return null;
  }

  const handleExtendSession = () => {
    // This would typically make an API call to refresh the token
    // For now, we'll just dismiss the warning
    dismissWarning();
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4 max-w-sm">
      <div className="flex items-start">
        <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            Session Expiring Soon
          </h3>
          <p className="mt-1 text-sm text-yellow-700">
            Your session will expire in {formatTimeLeft(timeLeft)}
          </p>
          <div className="mt-3 flex space-x-2">
            <button
              onClick={handleExtendSession}
              className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded text-sm font-medium"
            >
              Stay Logged In
            </button>
            <button
              onClick={handleLogout}
              className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded text-sm font-medium"
            >
              Logout
            </button>
          </div>
        </div>
        <button
          onClick={dismissWarning}
          className="ml-2 text-yellow-400 hover:text-yellow-600"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
```

### 4. Create Authentication Testing Utilities

#### File: `src/utils/authTest.ts` (NEW FILE)
**Action**: Create this new file:

```typescript
import { AuthService } from '../services/authService';
import { SecureTokenStorage } from './tokenStorage';

export interface AuthTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

export class AuthTestUtils {
  /**
   * Test complete authentication flow
   */
  static async testAuthFlow(): Promise<AuthTestResult[]> {
    const results: AuthTestResult[] = [];

    try {
      // Test 1: Login
      console.log('Testing login...');
      const loginResult = await AuthService.login({
        username: 'admin',
        password: 'admin123'
      });

      results.push({
        success: true,
        message: 'Login successful',
        data: { hasTokens: !!(loginResult.access_token && loginResult.refresh_token) }
      });

      // Test 2: Get user profile
      console.log('Testing user profile fetch...');
      const user = await AuthService.getCurrentUser();

      results.push({
        success: true,
        message: 'User profile fetch successful',
        data: { userId: user.id, username: user.username }
      });

      // Test 3: Token refresh
      console.log('Testing token refresh...');
      const refreshResult = await AuthService.refreshToken(loginResult.refresh_token);

      results.push({
        success: true,
        message: 'Token refresh successful',
        data: { hasNewToken: !!refreshResult.access_token }
      });

      // Test 4: Token storage
      console.log('Testing secure token storage...');
      SecureTokenStorage.setTokens(refreshResult.access_token, refreshResult.refresh_token);
      const storedToken = SecureTokenStorage.getAccessToken();

      results.push({
        success: !!storedToken,
        message: storedToken ? 'Token storage successful' : 'Token storage failed',
        data: { tokenStored: !!storedToken }
      });

    } catch (error) {
      results.push({
        success: false,
        message: 'Authentication test failed',
        error: error
      });
    }

    return results;
  }

  /**
   * Test token validation
   */
  static testTokenValidation(): AuthTestResult {
    try {
      const token = SecureTokenStorage.getAccessToken();

      if (!token) {
        return {
          success: false,
          message: 'No token found'
        };
      }

      const isValid = SecureTokenStorage.isValidTokenFormat(token);
      const isExpired = SecureTokenStorage.isTokenExpired(token);
      const expiration = SecureTokenStorage.getTokenExpiration(token);

      return {
        success: isValid && !isExpired,
        message: `Token validation: ${isValid ? 'valid format' : 'invalid format'}, ${isExpired ? 'expired' : 'not expired'}`,
        data: {
          isValid,
          isExpired,
          expiration: expiration?.toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Token validation failed',
        error
      };
    }
  }

  /**
   * Test security features
   */
  static async testSecurityFeatures(username: string = 'testuser'): Promise<AuthTestResult[]> {
    const { SecurityUtils } = await import('./securityUtils');
    const results: AuthTestResult[] = [];

    try {
      // Test login attempt tracking
      SecurityUtils.recordLoginAttempt(username, false);
      const failedAttempts = SecurityUtils.getRecentFailedAttempts(username);

      results.push({
        success: failedAttempts > 0,
        message: `Login attempt tracking: ${failedAttempts} failed attempts recorded`,
        data: { failedAttempts }
      });

      // Test lockout functionality
      const isLockedOut = SecurityUtils.isUserLockedOut(username);

      results.push({
        success: true,
        message: `User lockout status: ${isLockedOut ? 'locked out' : 'not locked out'}`,
        data: { isLockedOut }
      });

      // Test password validation
      const passwordTest = SecurityUtils.validatePasswordStrength('TestPassword123!');

      results.push({
        success: passwordTest.isValid,
        message: `Password validation: ${passwordTest.isValid ? 'passed' : 'failed'}`,
        data: { score: passwordTest.score, errors: passwordTest.errors }
      });

    } catch (error) {
      results.push({
        success: false,
        message: 'Security features test failed',
        error
      });
    }

    return results;
  }

  /**
   * Run all tests
   */
  static async runAllTests(): Promise<void> {
    console.group('🔐 Authentication Tests');

    try {
      // Test authentication flow
      console.group('Authentication Flow Tests');
      const authResults = await this.testAuthFlow();
      authResults.forEach(result => {
        console.log(result.success ? '✅' : '❌', result.message, result.data || result.error);
      });
      console.groupEnd();

      // Test token validation
      console.group('Token Validation Tests');
      const tokenResult = this.testTokenValidation();
      console.log(tokenResult.success ? '✅' : '❌', tokenResult.message, tokenResult.data || tokenResult.error);
      console.groupEnd();

      // Test security features
      console.group('Security Features Tests');
      const securityResults = await this.testSecurityFeatures();
      securityResults.forEach(result => {
        console.log(result.success ? '✅' : '❌', result.message, result.data || result.error);
      });
      console.groupEnd();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }

    console.groupEnd();
  }
}

// Export convenience function
export const testAuthFlow = AuthTestUtils.testAuthFlow;
export const runAllAuthTests = AuthTestUtils.runAllTests;
```

### 5. Update AuthService to Use SecureTokenStorage

#### File: `src/services/authService.ts`
**Action**: Replace the logout method (lines 66-70) with:

```typescript
static logout(): void {
  // Import SecureTokenStorage dynamically to avoid circular dependencies
  import('../utils/tokenStorage').then(({ SecureTokenStorage }) => {
    SecureTokenStorage.clearTokens();
  });
}
```

### 6. Integration Instructions

#### Step 1: Update Main App Component
**File**: `src/App.tsx`
**Action**: Add the following imports at the top:

```typescript
import { useTokenExpiration } from './hooks/useTokenExpiration';
import { useIdleTimeout } from './hooks/useIdleTimeout';
import SessionWarning from './components/SessionWarning';
```

**Action**: Inside the `AppRoutes` component (after line 38), add:

```typescript
function AppRoutes() {
  const { user } = useAuth();

  // Add these hooks
  useTokenExpiration();
  useIdleTimeout({
    onIdle: () => console.log('User is idle'),
    onActive: () => console.log('User is active')
  });

  // ... rest of existing code

  return (
    <>
      <SessionWarning />
      {/* existing Routes JSX */}
      <Routes>
        {/* ... existing routes */}
      </Routes>
    </>
  );
}
```

#### Step 2: Add Testing to Development
**File**: Create `src/dev/AuthTester.tsx` (NEW FILE):

```typescript
import React from 'react';
import { runAllAuthTests } from '../utils/authTest';

export default function AuthTester() {
  const handleRunTests = async () => {
    await runAllAuthTests();
  };

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={handleRunTests}
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow-lg text-sm"
      >
        🧪 Run Auth Tests
      </button>
    </div>
  );
}
```

#### Step 3: Add AuthTester to Layout
**File**: `src/components/Layout.tsx`
**Action**: Import and add AuthTester component:

```typescript
import AuthTester from '../dev/AuthTester';

// Add <AuthTester /> before the closing div in your Layout component
```

### 7. Environment Variables Update
**File**: `.env`
**Action**: Ensure these variables are set:

```env
VITE_SESSION_TIMEOUT=3600000
VITE_TOKEN_REFRESH_THRESHOLD=300000
```

## Testing the Implementation

1. **Test Token Expiration**:
   - Login and wait, check console for expiration messages
   - Manually expire token in localStorage to test auto-logout

2. **Test Idle Timeout**:
   - Login and remain idle for the configured time
   - Should auto-logout after idle period

3. **Test Session Warning**:
   - Login and wait until 5 minutes before token expiry
   - Should show warning popup

4. **Test Authentication Flow**:
   - Open browser console
   - Click "Run Auth Tests" button (development only)
   - Check console for test results

## Benefits of This Implementation

1. **Enhanced Security**: Automatic logout on token expiration and idle timeout
2. **Better UX**: Session warnings give users chance to extend sessions
3. **Robust Testing**: Comprehensive test utilities for debugging
4. **Activity Monitoring**: Tracks user activity and session duration
5. **Secure Storage**: All hooks now use SecureTokenStorage instead of direct localStorage
6. **Environment Aware**: Different behaviors for development vs production

This implementation provides enterprise-level session management with comprehensive security features and testing capabilities.
```
