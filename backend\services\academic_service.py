# services/academic_service.py
from typing import List, Dict, Optional
from datetime import date, datetime, timedelta
from sqlalchemy.orm import Session
from crud.grade import grade
from crud.attendance import attendance
from crud.course import course
from crud.student import student
from crud.subject import subject

class AcademicService:
    @staticmethod
    def calculate_student_average(db: Session, student_id: int, course_id: int = None) -> Dict:
        """Calculate student average for all courses or specific course"""
        if course_id:
            grades = grade.get_by_student_and_course(db, eleve_id=student_id, cours_id=course_id)
            course_obj = course.get(db, course_id)
            subject_obj = subject.get(db, course_obj.matiere_id) if course_obj else None
            
            if not grades:
                return {"average": 0, "total_grades": 0, "subject": subject_obj.nom if subject_obj else None}
            
            # Apply subject coefficient
            coef = subject_obj.coef if subject_obj else 1.0
            weighted_average = (sum(g.note for g in grades) / len(grades)) * coef
            
            return {
                "average": round(weighted_average, 2),
                "total_grades": len(grades),
                "subject": subject_obj.nom if subject_obj else None,
                "coefficient": coef
            }
        else:
            # Calculate overall average across all courses
            all_grades = grade.get_by_student(db, eleve_id=student_id)
            if not all_grades:
                return {"overall_average": 0, "total_grades": 0}
            
            # Group grades by course and apply coefficients
            grades_by_course = {}
            for g in all_grades:
                if g.cours_id not in grades_by_course:
                    grades_by_course[g.cours_id] = []
                grades_by_course[g.cours_id].append(g.note)
            
            weighted_sum = 0
            total_coef = 0
            
            for course_id, course_grades in grades_by_course.items():
                course_obj = course.get(db, course_id)
                if course_obj:
                    subject_obj = subject.get(db, course_obj.matiere_id)
                    coef = subject_obj.coef if subject_obj else 1.0
                    course_average = sum(course_grades) / len(course_grades)
                    weighted_sum += course_average * coef
                    total_coef += coef
            
            overall_average = weighted_sum / total_coef if total_coef > 0 else 0
            
            return {
                "overall_average": round(overall_average, 2),
                "total_grades": len(all_grades),
                "courses_count": len(grades_by_course)
            }
    
    @staticmethod
    def get_class_ranking(db: Session, class_id: int) -> List[Dict]:
        """Get student ranking for a class based on overall averages"""
        students_in_class = student.get_by_class(db, classe_id=class_id)
        student_averages = []
        
        for student_obj in students_in_class:
            if student_obj.is_active:
                avg_data = AcademicService.calculate_student_average(db, student_obj.id)
                student_averages.append({
                    "student_id": student_obj.id,
                    "name": f"{student_obj.prenom} {student_obj.nom}",
                    "matricule": student_obj.matricule,
                    "average": avg_data["overall_average"],
                    "total_grades": avg_data["total_grades"]
                })
        
        # Sort by average (descending)
        student_averages.sort(key=lambda x: x["average"], reverse=True)
        
        # Add ranking
        for i, student_data in enumerate(student_averages):
            student_data["rank"] = i + 1
        
        return student_averages
    
    @staticmethod
    def get_attendance_report(db: Session, class_id: int = None, start_date: date = None, end_date: date = None) -> Dict:
        """Generate attendance report for class or overall"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        if class_id:
            students_in_class = student.get_by_class(db, classe_id=class_id)
            report_data = {"class_id": class_id, "students": []}
            
            for student_obj in students_in_class:
                if student_obj.is_active:
                    student_attendance = attendance.get_by_student_and_date_range(
                        db, eleve_id=student_obj.id, start_date=start_date, end_date=end_date
                    )
                    
                    total_sessions = len(student_attendance)
                    present_count = len([a for a in student_attendance if a.statut == 'present'])
                    absent_count = len([a for a in student_attendance if a.statut == 'absent'])
                    late_count = len([a for a in student_attendance if a.statut == 'late'])
                    
                    attendance_rate = (present_count / total_sessions * 100) if total_sessions > 0 else 0
                    
                    report_data["students"].append({
                        "student_id": student_obj.id,
                        "name": f"{student_obj.prenom} {student_obj.nom}",
                        "matricule": student_obj.matricule,
                        "total_sessions": total_sessions,
                        "present": present_count,
                        "absent": absent_count,
                        "late": late_count,
                        "attendance_rate": round(attendance_rate, 2)
                    })
        else:
            # Overall attendance report
            all_attendance = attendance.get_by_date_range(db, start_date=start_date, end_date=end_date)
            
            stats_by_status = {}
            for a in all_attendance:
                if a.statut not in stats_by_status:
                    stats_by_status[a.statut] = 0
                stats_by_status[a.statut] += 1
            
            total_records = len(all_attendance)
            overall_rate = (stats_by_status.get('present', 0) / total_records * 100) if total_records > 0 else 0
            
            report_data = {
                "period": {"start_date": start_date, "end_date": end_date},
                "overall_stats": stats_by_status,
                "total_records": total_records,
                "overall_attendance_rate": round(overall_rate, 2)
            }
        
        return report_data
    
    @staticmethod
    def get_subject_performance(db: Session, subject_id: int) -> Dict:
        """Get performance statistics for a specific subject"""
        subject_obj = subject.get(db, subject_id)
        if not subject_obj:
            return {"error": "Subject not found"}
        
        # Get all courses for this subject
        subject_courses = course.get_by_subject(db, matiere_id=subject_id)
        
        all_grades = []
        students_count = 0
        
        for course_obj in subject_courses:
            course_grades = grade.get_by_course(db, cours_id=course_obj.id)
            all_grades.extend(course_grades)
            
            # Count unique students
            unique_students = set(g.eleve_id for g in course_grades)
            students_count += len(unique_students)
        
        if not all_grades:
            return {
                "subject": subject_obj.nom,
                "no_data": True
            }
        
        # Calculate statistics
        grades_values = [g.note for g in all_grades]
        average_grade = sum(grades_values) / len(grades_values)
        min_grade = min(grades_values)
        max_grade = max(grades_values)
        
        # Grade distribution
        grade_ranges = {
            "excellent": len([g for g in grades_values if g >= 16]),
            "good": len([g for g in grades_values if 14 <= g < 16]),
            "average": len([g for g in grades_values if 10 <= g < 14]),
            "below_average": len([g for g in grades_values if g < 10])
        }
        
        return {
            "subject": {
                "id": subject_obj.id,
                "name": subject_obj.nom,
                "coefficient": subject_obj.coef
            },
            "statistics": {
                "total_grades": len(all_grades),
                "students_count": students_count,
                "average_grade": round(average_grade, 2),
                "min_grade": min_grade,
                "max_grade": max_grade
            },
            "grade_distribution": grade_ranges,
            "courses_count": len(subject_courses)
        }
