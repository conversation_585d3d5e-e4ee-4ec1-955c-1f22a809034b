from fastapi import FastAPI
from routes import auth, user, school_years, subjects, teachers, classes, students, payments, timetables

app = FastAPI(
    title="École AI - School Management System",
    description="Comprehensive school management system with authentication, student management, grades, attendance, and financial tracking",
    version="1.0.0"
)

# Authentication routes
app.include_router(auth.router)
app.include_router(user.router)

# School management routes
app.include_router(school_years.router)
app.include_router(subjects.router)
app.include_router(teachers.router)
app.include_router(classes.router)
app.include_router(students.router)
app.include_router(payments.router)
app.include_router(timetables.router)

from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Vite default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
