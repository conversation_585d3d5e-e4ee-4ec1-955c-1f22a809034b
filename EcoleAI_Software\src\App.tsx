import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import Students from './components/Students';
import Certificates from './components/Certificates';
import Reports from './components/Reports';
import Evaluations from './components/Evaluations';
import Statistics from './components/Statistics';
import StudentList from './components/StudentList';
import Payments from './components/Payments';
import Finances from './components/Finances';
import Teachers from './components/Teachers';
import Payroll from './components/Payroll';
import Absences from './components/Absences';
import Notifications from './components/Notifications';
import StudentRecord from './components/StudentRecord';
import Forms from './components/Forms';
import Backup from './components/Backup';
import Layout from './components/Layout';

// New hierarchical components
import StudentEnrollmentWizard from './components/students/enrollment/StudentEnrollmentWizard';
import StudentDirectory from './components/students/StudentDirectory';
import SchoolYearManagement from './components/academic/SchoolYearManagementSimple';
import ClassesManagement from './components/academic/ClassesManagement';
import SubjectsManagement from './components/academic/SubjectsManagement';

import { AuthProvider, useAuth } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import ProtectedRoute from './components/ProtectedRoute';
import { useTokenExpiration } from './hooks/useTokenExpiration';
import { useIdleTimeout } from './hooks/useIdleTimeout';
import SessionWarning from './components/SessionWarning';
import { useEnhancedSession } from './hooks/useEnhancedSession';

// function PrivateRoute({ children, allowedRoles }: { children: React.ReactNode; allowedRoles: string[] }) {
//   const { user } = useAuth();

//   if (!user) {
//     return <Navigate to="/login" replace />;
//   }

//   if (!allowedRoles.includes(user.role)) {
//     return <Navigate to="/dashboard" replace />;
//   }

//   return <>{children}</>;
// }

function AppRoutes() {
  const { user } = useAuth();

  useTokenExpiration();
  useIdleTimeout({
    onIdle: () => console.log('User is idle'),
    onActive: () => console.log('User is active')
  });

  // Enhanced session management
  const sessionHealth = useEnhancedSession().getSessionHealth();

  // Log session health in development
  if (import.meta.env.DEV) {
    console.log('Session Health:', sessionHealth);
  }


  if (!user) {
    return <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="*" element={<Navigate to="/login" replace />} />
    </Routes>;
  }

  return (
    <Layout>
      <SessionWarning />
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />

        {/* New Hierarchical Routes */}
        {/* Student Management */}
        <Route path="/students/enrollment" element={<StudentEnrollmentWizard />} />
        <Route path="/students/directory" element={<StudentDirectory />} />
        <Route path="/students/academic-reports" element={<Reports />} />
        <Route path="/students/performance" element={<Statistics />} />
        <Route path="/students/absences" element={<Absences />} />

        {/* Academic Management */}
        <Route path="/academic/school-years" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <SchoolYearManagement />
          </ProtectedRoute>
        } />
        <Route path="/academic/subjects" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <SubjectsManagement />
          </ProtectedRoute>
        } />
        <Route path="/academic/teachers" element={<Teachers />} />
        <Route path="/academic/classes" element={
          <ProtectedRoute allowedRoles={['admin', 'secretary']}>
            <ClassesManagement />
          </ProtectedRoute>
        } />

        {/* Financial Management */}
        <Route path="/financial/payments" element={<Payments />} />
        <Route path="/financial/dashboard" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Finances />
          </ProtectedRoute>
        } />
        <Route path="/financial/fees" element={
          <div className="p-8">
            <h1 className="text-2xl font-bold">Fee Management</h1>
            <p>This page will be implemented soon.</p>
          </div>
        } />

        {/* System Administration */}
        <Route path="/admin/users" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <div className="p-8">
              <h1 className="text-2xl font-bold">User Management</h1>
              <p>This page will be implemented soon.</p>
            </div>
          </ProtectedRoute>
        } />
        <Route path="/admin/notifications" element={<Notifications />} />
        <Route path="/admin/backup" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Backup />
          </ProtectedRoute>
        } />

        {/* Legacy Routes (commented out but preserved) */}
        {/* <Route path="/eleves" element={<Students />} /> */}
        {/* <Route path="/certificats" element={<Certificates />} /> */}
        {/* <Route path="/bulletins" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Reports />
          </ProtectedRoute>
        } /> */}
        {/* <Route path="/evaluations" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Evaluations />
          </ProtectedRoute>
        } /> */}
        {/* <Route path="/statistiques" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Statistics />
          </ProtectedRoute>
        } /> */}
        {/* <Route path="/liste-eleves" element={<StudentList />} /> */}
        {/* <Route path="/paiements" element={<Payments />} /> */}
        {/* <Route path="/finances" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Finances />
          </ProtectedRoute>
        } /> */}
        {/* <Route path="/professeurs" element={<Teachers />} /> */}
        {/* <Route path="/fiches-paie" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Payroll />
          </ProtectedRoute>
        } /> */}
        {/* <Route path="/absences" element={<Absences />} /> */}
        {/* <Route path="/notifications" element={<Notifications />} /> */}
        <Route path="/dossier/:matricule" element={<StudentRecord />} />
        {/* <Route path="/formulaires" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Forms />
          </ProtectedRoute>
        } /> */}
        {/* <Route path="/sauvegarde" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Backup />
          </ProtectedRoute>
        } /> */}

        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/login" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Layout>
  );
}

function App() {
  return (
    <AuthProvider>
      <NotificationProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <AppRoutes />
          </div>
        </Router>
      </NotificationProvider>
    </AuthProvider>
  );
}

export default App;