# crud/attendance.py
from typing import List, Dict
from datetime import date
from sqlalchemy.orm import Session
from sqlalchemy import func
from crud.base import CRUDBase
from models.attendance import Attendance
from schemas.attendance import AttendanceCreate, AttendanceUpdate

class CRUDAttendance(CRUDBase[Attendance, AttendanceCreate, AttendanceUpdate]):
    def get_by_student(self, db: Session, *, eleve_id: int) -> List[Attendance]:
        return db.query(Attendance).filter(Attendance.eleve_id == eleve_id).all()
    
    def get_by_course(self, db: Session, *, cours_id: int) -> List[Attendance]:
        return db.query(Attendance).filter(Attendance.cours_id == cours_id).all()
    
    def get_by_date(self, db: Session, *, date: date) -> List[Attendance]:
        return db.query(Attendance).filter(Attendance.date == date).all()
    
    def get_by_student_and_date_range(
        self, db: Session, *, eleve_id: int, start_date: date, end_date: date
    ) -> List[Attendance]:
        return db.query(Attendance).filter(
            Attendance.eleve_id == eleve_id,
            Attendance.date >= start_date,
            Attendance.date <= end_date
        ).all()
    
    def get_by_status(self, db: Session, *, statut: str) -> List[Attendance]:
        return db.query(Attendance).filter(Attendance.statut == statut).all()
    
    def get_attendance_stats_by_student(
        self, db: Session, *, eleve_id: int
    ) -> Dict[str, int]:
        stats = db.query(
            Attendance.statut,
            func.count(Attendance.id).label('count')
        ).filter(
            Attendance.eleve_id == eleve_id
        ).group_by(Attendance.statut).all()
        
        return {stat.statut: stat.count for stat in stats}

attendance = CRUDAttendance(Attendance)
