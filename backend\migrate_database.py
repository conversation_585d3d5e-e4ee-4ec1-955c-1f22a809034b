#!/usr/bin/env python3
"""
Database migration script for École AI School Management System
Handles database schema updates and data migrations
"""

import os
import sys
from datetime import datetime
from sqlalchemy import text
from db import engine, SessionLocal, Base

def backup_database():
    """Create a backup of the current database"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"ecoleai_backup_{timestamp}.sql"
    
    # PostgreSQL backup command
    db_url = os.getenv('POSTGRES_DB', 'fastapi_db')
    db_user = os.getenv('POSTGRES_USER', 'admin')
    db_host = os.getenv('POSTGRES_HOST', 'localhost')
    db_port = os.getenv('POSTGRES_PORT', '5432')
    
    backup_cmd = f"pg_dump -h {db_host} -p {db_port} -U {db_user} -d {db_url} > {backup_name}"
    
    print(f"Creating database backup: {backup_name}")
    print(f"Command: {backup_cmd}")
    print("Note: Run this command manually if needed for production systems")
    
    return backup_name

def check_table_exists(table_name: str) -> bool:
    """Check if a table exists in the database"""
    db = SessionLocal()
    try:
        result = db.execute(text(f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = '{table_name}'
            );
        """))
        return result.scalar()
    except Exception as e:
        print(f"Error checking table {table_name}: {e}")
        return False
    finally:
        db.close()

def migrate_existing_users():
    """Migrate existing user data if needed"""
    db = SessionLocal()
    try:
        # Check if users table exists and has data
        if check_table_exists('users'):
            result = db.execute(text("SELECT COUNT(*) FROM users"))
            user_count = result.scalar()
            print(f"Found {user_count} existing users")
            
            # Add any necessary user data migrations here
            # For example, adding default roles or updating schema
            
        return True
    except Exception as e:
        print(f"Error migrating users: {e}")
        return False
    finally:
        db.close()

def create_indexes():
    """Create database indexes for better performance"""
    db = SessionLocal()
    try:
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_students_matricule ON students(matricule);",
            "CREATE INDEX IF NOT EXISTS idx_students_classe_id ON students(classe_id);",
            "CREATE INDEX IF NOT EXISTS idx_students_active ON students(is_active);",
            "CREATE INDEX IF NOT EXISTS idx_grades_student_id ON grades(eleve_id);",
            "CREATE INDEX IF NOT EXISTS idx_grades_course_id ON grades(cours_id);",
            "CREATE INDEX IF NOT EXISTS idx_grades_date ON grades(date);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendances(eleve_id);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendances(date);",
            "CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(eleve_id);",
            "CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(statut);",
            "CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(date_paiement);",
            "CREATE INDEX IF NOT EXISTS idx_courses_teacher_id ON courses(enseignant_id);",
            "CREATE INDEX IF NOT EXISTS idx_courses_class_id ON courses(classe_id);",
            "CREATE INDEX IF NOT EXISTS idx_teachers_email ON teachers(email);",
            "CREATE INDEX IF NOT EXISTS idx_teachers_active ON teachers(is_active);"
        ]
        
        for index_sql in indexes:
            try:
                db.execute(text(index_sql))
                print(f"✅ Created index: {index_sql.split('idx_')[1].split(' ')[0]}")
            except Exception as e:
                print(f"⚠️ Index creation warning: {e}")
        
        db.commit()
        return True
    except Exception as e:
        print(f"Error creating indexes: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def run_migration():
    """Run the complete database migration"""
    print("🔧 Starting École AI Database Migration")
    print("=" * 50)
    
    # Step 1: Backup (informational)
    backup_name = backup_database()
    
    # Step 2: Create/update tables
    print("\n📋 Creating/updating database tables...")
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created/updated successfully")
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False
    
    # Step 3: Migrate existing data
    print("\n👥 Migrating existing user data...")
    if migrate_existing_users():
        print("✅ User data migration completed")
    else:
        print("❌ User data migration failed")
        return False
    
    # Step 4: Create performance indexes
    print("\n⚡ Creating database indexes for performance...")
    if create_indexes():
        print("✅ Database indexes created successfully")
    else:
        print("❌ Index creation failed")
        return False
    
    # Step 5: Verify migration
    print("\n🔍 Verifying migration...")
    required_tables = [
        'users', 'refresh_tokens', 'school_years', 'subjects', 
        'teachers', 'classes', 'students', 'courses', 
        'grades', 'attendances', 'payments'
    ]
    
    missing_tables = []
    for table in required_tables:
        if not check_table_exists(table):
            missing_tables.append(table)
    
    if missing_tables:
        print(f"❌ Missing tables: {', '.join(missing_tables)}")
        return False
    else:
        print("✅ All required tables exist")
    
    print("\n🎉 Database migration completed successfully!")
    print("=" * 50)
    print("Next steps:")
    print("1. Run 'python init_sample_data.py' to create sample data")
    print("2. Start the server with 'uvicorn main:app --reload'")
    print("3. Run tests with 'python test_school_management.py'")
    
    return True

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
