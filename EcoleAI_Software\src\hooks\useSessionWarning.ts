import { useState, useEffect, useCallback } from 'react';
import { useSession } from './useSession';

interface UseSessionWarningOptions {
    warningTime?: number;
    onWarning?: (timeLeft: number) => void;
    onExpired?: () => void;
}

export function useSessionWarning(options: UseSessionWarningOptions = {}) {
    const { warningTime = 5 * 60 * 1000, onWarning, onExpired } = options;
    const { timeUntilExpiry, isAuthenticated, sessionExpiry } = useSession();
    const [showWarning, setShowWarning] = useState(false);
    const [timeLeft, setTimeLeft] = useState<number | null>(null);

    const dismissWarning = useCallback(() => {
        setShowWarning(false);
        setTimeLeft(null);
    }, []);

    useEffect(() => {
        if (!isAuthenticated || !timeUntilExpiry) {
            setShowWarning(false);
            setTimeLeft(null);
            return;
        }

        if (timeUntilExpiry <= 0) {
            setShowWarning(false);
            setTimeLeft(null);
            onExpired?.();
            return;
        }

        // If session has been extended (timeUntilExpiry is much larger than warningTime), dismiss warning
        if (timeUntilExpiry > warningTime && showWarning) {
            setShowWarning(false);
            setTimeLeft(null);
            return;
        }

        if (timeUntilExpiry <= warningTime && !showWarning) {
            setShowWarning(true);
            setTimeLeft(timeUntilExpiry);
            onWarning?.(timeUntilExpiry);
        }

        if (showWarning) {
            setTimeLeft(timeUntilExpiry);
        }

        // Update every second when warning is shown
        if (showWarning && timeUntilExpiry > 0) {
            const interval = setInterval(() => {
                const currentTimeLeft = sessionExpiry ? sessionExpiry.getTime() - Date.now() : 0;
                if (currentTimeLeft <= 0) {
                    setShowWarning(false);
                    setTimeLeft(null);
                    onExpired?.();
                } else if (currentTimeLeft > warningTime) {
                    // Session was extended, dismiss warning
                    setShowWarning(false);
                    setTimeLeft(null);
                } else {
                    setTimeLeft(currentTimeLeft);
                }
            }, 1000);

            return () => clearInterval(interval);
        }
    }, [timeUntilExpiry, isAuthenticated, warningTime, showWarning, onWarning, onExpired, sessionExpiry]);

    return {
        showWarning,
        timeLeft,
        dismissWarning,
        formatTimeLeft: (ms: number | null) => {
            if (!ms) return '';
            const minutes = Math.floor(ms / 60000);
            const seconds = Math.floor((ms % 60000) / 1000);
            return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    };
}