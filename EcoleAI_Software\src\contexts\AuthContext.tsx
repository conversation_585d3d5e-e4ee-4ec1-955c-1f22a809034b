import React, { createContext, useContext, useState, useEffect } from 'react';
import { AuthService, User } from '../services/authService';
import { SecureTokenStorage } from '../utils/tokenStorage';
import { SecurityUtils } from '../utils/securityUtils';

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<{ success: boolean; error?: string; }>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
  isUserLockedOut: (username: string) => boolean;
  getRemainingLockoutTime: (username: string) => number;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    if (SecureTokenStorage.hasValidTokens()) {
      try {
        const userData = await AuthService.getCurrentUser();
        setUser(userData);
        SecureTokenStorage.setUserData(userData);
      } catch (error) {
        console.error('Token verification failed:', error);
        const refreshToken = SecureTokenStorage.getRefreshToken();
        if (refreshToken) {
          try {
            const authResponse = await AuthService.refreshToken(refreshToken);
            SecureTokenStorage.setTokens(authResponse.access_token, authResponse.refresh_token);

            const userData = await AuthService.getCurrentUser();
            setUser(userData);
            SecureTokenStorage.setUserData(userData);
          } catch (refreshError) {
            console.error('Refresh failed:', refreshError);
            SecureTokenStorage.clearTokens();
          }
        }
      }
    } else {
      const userData = SecureTokenStorage.getUserData();
      if (userData) {
        setUser(userData as User);
      }
    }

    setIsLoading(false);
  };

  const login = async (username: string, password: string): Promise<{ success: boolean; error?: string; }> => {
    if (SecurityUtils.isUserLockedOut(username)) {
      const remainingTime = SecurityUtils.getRemainingLockoutTime(username);
      const minutes = Math.ceil(remainingTime / 60000);
      return {
        success: false,
        error: `Compte verrouillé. Réessayez dans ${minutes} minute(s).`
      };
    }

    try {
      const authResponse = await AuthService.login({ username, password });

      SecureTokenStorage.setTokens(authResponse.access_token, authResponse.refresh_token);

      const userData = await AuthService.getCurrentUser();
      setUser(userData);

      SecureTokenStorage.setUserData(userData);

      SecurityUtils.recordLoginAttempt(username, true);

      return { success: true };
    } catch (error) {
      console.error('Login failed:', error);

      SecurityUtils.recordLoginAttempt(username, false);

      if (SecurityUtils.isUserLockedOut(username)) {
        const remainingTime = SecurityUtils.getRemainingLockoutTime(username);
        const minutes = Math.ceil(remainingTime / 60000);
        return {
          success: false,
          error: `Trop de tentatives échouées. Compte verrouillé pour ${minutes} minute(s).`
        };
      }

      const remainingAttempts = SecurityUtils.getRecentFailedAttempts(username);
      return {
        success: false,
        error: `Identifiants incorrects. ${remainingAttempts} tentative(s) échouée(s).`
      };
    }
  };

  const logout = () => {
    SecureTokenStorage.clearTokens();
    SecurityUtils.clearSecurityData();
    setUser(null);
  };

  const isUserLockedOut = (username: string): boolean => {
    return SecurityUtils.isUserLockedOut(username);
  };

  const getRemainingLockoutTime = (username: string): number => {
    return SecurityUtils.getRemainingLockoutTime(username);
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isLoading,
      isAuthenticated,
      isUserLockedOut,
      getRemainingLockoutTime
    }}>
      {children}
    </AuthContext.Provider>
  );
}