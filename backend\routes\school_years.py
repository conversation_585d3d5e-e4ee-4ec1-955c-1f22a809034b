# routes/school_years.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from utils.security import get_current_active_user, get_db
from crud.school_year import school_year
from schemas.school_year import SchoolYearCreate, SchoolYearRead, SchoolYearUpdate

router = APIRouter(prefix="/api/school-years", tags=["School Years"])

@router.get("/", response_model=List[SchoolYearRead])
async def get_school_years(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all school years"""
    return school_year.get_multi(db, skip=skip, limit=limit)

@router.get("/active", response_model=SchoolYearRead)
async def get_active_school_year(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get the currently active school year"""
    active_year = school_year.get_active(db)
    if not active_year:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active school year found"
        )
    return active_year

@router.get("/{school_year_id}", response_model=SchoolYearRead)
async def get_school_year(
    school_year_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific school year by ID"""
    db_school_year = school_year.get(db, school_year_id)
    if not db_school_year:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="School year not found"
        )
    return db_school_year

@router.post("/", response_model=SchoolYearRead)
async def create_school_year(
    school_year_in: SchoolYearCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new school year"""
    # Check if school year already exists
    existing = school_year.get_by_annee(db, annee=school_year_in.annee)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="School year already exists"
        )
    
    return school_year.create(db, obj_in=school_year_in)

@router.put("/{school_year_id}", response_model=SchoolYearRead)
async def update_school_year(
    school_year_id: int,
    school_year_in: SchoolYearUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a school year"""
    db_school_year = school_year.get(db, school_year_id)
    if not db_school_year:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="School year not found"
        )
    
    return school_year.update(db, db_obj=db_school_year, obj_in=school_year_in)

@router.post("/{school_year_id}/activate", response_model=SchoolYearRead)
async def activate_school_year(
    school_year_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Set a school year as active (deactivates all others)"""
    db_school_year = school_year.set_active(db, school_year_id=school_year_id)
    if not db_school_year:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="School year not found"
        )
    return db_school_year

@router.delete("/{school_year_id}")
async def delete_school_year(
    school_year_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a school year"""
    db_school_year = school_year.get(db, school_year_id)
    if not db_school_year:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="School year not found"
        )
    
    # Check if it's the active year
    if db_school_year.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete the active school year"
        )
    
    school_year.remove(db, id=school_year_id)
    return {"message": "School year deleted successfully"}
