# schemas/subject.py
from pydantic import BaseModel, validator
from typing import Optional

class SubjectBase(BaseModel):
    nom: str
    coef: Optional[float] = 1.0
    description: Optional[str] = None

    @validator('nom')
    def validate_nom(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Subject name must be at least 2 characters long')
        return v.strip()

    @validator('coef')
    def validate_coef(cls, v):
        if v is not None and (v <= 0 or v > 10):
            raise ValueError('Coefficient must be between 0 and 10')
        return v

class SubjectCreate(SubjectBase):
    pass

class SubjectUpdate(BaseModel):
    nom: Optional[str] = None
    coef: Optional[float] = None
    description: Optional[str] = None

class SubjectRead(SubjectBase):
    id: int

    class Config:
        from_attributes = True
