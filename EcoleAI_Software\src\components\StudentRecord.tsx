import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { 
  User, 
  FileText, 
  CreditCard, 
  Calendar, 
  BookOpen, 
  Download, 
  Edit, 
  Phone, 
  Mail, 
  MapPin,
  GraduationCap,
  TrendingUp
} from 'lucide-react';

interface StudentData {
  id: string;
  matricule: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  class: string;
  photo?: string;
  parentName: string;
  parentPhone: string;
  parentEmail?: string;
  address?: string;
  enrollmentDate: string;
  status: 'active' | 'inactive';
}

interface Payment {
  id: string;
  type: string;
  amount: number;
  date: string;
  status: 'completed' | 'pending';
}

interface Absence {
  id: string;
  date: string;
  reason: string;
  justified: boolean;
}

interface Grade {
  id: string;
  subject: string;
  grade: number;
  coefficient: number;
  period: string;
}

const mockStudent: StudentData = {
  id: '1',
  matricule: 'EAI2024001',
  firstName: 'Solo',
  lastName: 'RASOLONDRAIBE',
  dateOfBirth: '2010-05-15',
  gender: 'M',
  class: '6ème A',
  parentName: 'RASOLONDRAIBE Paul',
  parentPhone: '+261 32 43 789 12',
  parentEmail: '<EMAIL>',
  address: 'Abidjan, Cocody, Riviera',
  enrollmentDate: '2023-09-01',
  status: 'active'
};

const mockPayments: Payment[] = [
  { id: '1', type: 'Frais de scolarité', amount: 45000, date: '2024-01-15', status: 'completed' },
  { id: '2', type: 'Uniforme scolaire', amount: 15000, date: '2024-01-10', status: 'completed' },
  { id: '3', type: 'Cantine', amount: 25000, date: '2024-02-01', status: 'pending' }
];

const mockAbsences: Absence[] = [
  { id: '1', date: '2024-01-15', reason: 'Maladie', justified: true },
  { id: '2', date: '2024-01-10', reason: 'Personnel', justified: false }
];

const mockGrades: Grade[] = [
  { id: '1', subject: 'Mathématiques', grade: 15.5, coefficient: 4, period: '1er Trimestre' },
  { id: '2', subject: 'Français', grade: 14.0, coefficient: 4, period: '1er Trimestre' },
  { id: '3', subject: 'Histoire-Géo', grade: 16.0, coefficient: 2, period: '1er Trimestre' }
];

export default function StudentRecord() {
  const { matricule } = useParams<{ matricule: string }>();
  const [activeTab, setActiveTab] = useState<'profile' | 'payments' | 'absences' | 'grades' | 'documents'>('profile');
  const [student] = useState<StudentData>(mockStudent);
  const [payments] = useState<Payment[]>(mockPayments);
  const [absences] = useState<Absence[]>(mockAbsences);
  const [grades] = useState<Grade[]>(mockGrades);

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR').format(amount) + ' AR';
  };

  const calculateAverage = () => {
    if (grades.length === 0) return 0;
    const totalPoints = grades.reduce((sum, grade) => sum + (grade.grade * grade.coefficient), 0);
    const totalCoefficients = grades.reduce((sum, grade) => sum + grade.coefficient, 0);
    return (totalPoints / totalCoefficients).toFixed(2);
  };

  const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const paidAmount = payments.filter(p => p.status === 'completed').reduce((sum, payment) => sum + payment.amount, 0);
  const pendingAmount = totalPayments - paidAmount;

  const tabs = [
    { id: 'profile', label: 'Profil', icon: User },
    { id: 'payments', label: 'Paiements', icon: CreditCard },
    { id: 'absences', label: 'Absences', icon: Calendar },
    { id: 'grades', label: 'Notes', icon: BookOpen },
    { id: 'documents', label: 'Documents', icon: FileText }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center">
              {student.photo ? (
                <img className="h-20 w-20 rounded-full object-cover" src={student.photo} alt="" />
              ) : (
                <span className="text-2xl font-medium text-gray-700">
                  {student.firstName[0]}{student.lastName[0]}
                </span>
              )}
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {student.firstName} {student.lastName}
              </h1>
              <p className="text-gray-600">
                {student.matricule} - {student.class}
              </p>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  student.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {student.status === 'active' ? 'Actif' : 'Inactif'}
                </span>
                <span className="text-sm text-gray-500">
                  Inscrit le {new Date(student.enrollmentDate).toLocaleDateString('fr-FR')}
                </span>
              </div>
            </div>
          </div>
          <div className="mt-4 md:mt-0">
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90"
                    style={{ backgroundColor: '#0a1186' }}>
              <Edit className="h-4 w-4 mr-2" />
              Modifier
            </button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Moyenne Générale</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{calculateAverage()}/20</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Paiements</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{formatAmount(paidAmount)}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <CreditCard className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Absences</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{absences.length}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <Calendar className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">En Attente</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{formatAmount(pendingAmount)}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <TrendingUp className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-5 w-5 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100">
        {activeTab === 'profile' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Informations Personnelles</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nom complet</label>
                  <p className="mt-1 text-sm text-gray-900">{student.firstName} {student.lastName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Matricule</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono">{student.matricule}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Date de naissance</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(student.dateOfBirth).toLocaleDateString('fr-FR')}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Sexe</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {student.gender === 'M' ? 'Masculin' : 'Féminin'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Classe</label>
                  <p className="mt-1 text-sm text-gray-900">{student.class}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Informations du Parent/Tuteur</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nom</label>
                  <p className="mt-1 text-sm text-gray-900">{student.parentName}</p>
                </div>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 text-gray-400 mr-2" />
                  <p className="text-sm text-gray-900">{student.parentPhone}</p>
                </div>
                {student.parentEmail && (
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-400 mr-2" />
                    <p className="text-sm text-gray-900">{student.parentEmail}</p>
                  </div>
                )}
                {student.address && (
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                    <p className="text-sm text-gray-900">{student.address}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payments' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Historique des Paiements</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type de Paiement
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Montant
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payments.map((payment) => (
                    <tr key={payment.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatAmount(payment.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(payment.date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          payment.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {payment.status === 'completed' ? 'Payé' : 'En attente'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Download className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'absences' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Historique des Absences</h3>
            <div className="space-y-4">
              {absences.map((absence) => (
                <div key={absence.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">
                      {new Date(absence.date).toLocaleDateString('fr-FR')}
                    </p>
                    <p className="text-sm text-gray-500">{absence.reason}</p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    absence.justified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {absence.justified ? 'Justifiée' : 'Non justifiée'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'grades' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Notes et Évaluations</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Matière
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Note
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Coefficient
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Période
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {grades.map((grade) => (
                    <tr key={grade.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {grade.subject}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-medium ${
                          grade.grade >= 15 ? 'text-green-600' :
                          grade.grade >= 10 ? 'text-yellow-600' :
                          'text-red-600'
                        }`}>
                          {grade.grade}/20
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {grade.coefficient}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {grade.period}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'documents' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Documents</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-blue-600 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Certificat de naissance</p>
                    <p className="text-sm text-gray-500">PDF - 2.1 MB</p>
                  </div>
                </div>
                <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                  Télécharger
                </button>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-green-600 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Relevé de notes</p>
                    <p className="text-sm text-gray-500">PDF - 1.5 MB</p>
                  </div>
                </div>
                <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                  Télécharger
                </button>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-purple-600 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Fiche d'inscription</p>
                    <p className="text-sm text-gray-500">PDF - 850 KB</p>
                  </div>
                </div>
                <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                  Télécharger
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}