# models/subject.py
from sqlalchemy import Column, Integer, String, Float
from db import Base
from sqlalchemy.orm import relationship

class Subject(Base):
    __tablename__ = "subjects"

    id = Column(Integer, primary_key=True, index=True)
    nom = Column(String, nullable=False, index=True)  # Mathematics, French, etc.
    coef = Column(Float, default=1.0)  # Subject coefficient for grade calculations
    description = Column(String, nullable=True)

    # Relationships
    courses = relationship("Course", back_populates="subject")
