import React, { useState, useEffect } from 'react';
import { Building, Plus, Search, Filter, Edit, Eye, Users, Loader, Trash2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { apiClient } from '../../config/api';

interface Class {
  id: number;
  nom: string;
  niveau: string;
  annee_scolaire_id: number;
  created_at?: string;
  updated_at?: string;
  annee_scolaire?: {
    id: number;
    annee: string;  // Backend uses 'annee' not 'nom'
  };
}

interface SchoolYear {
  id: number;
  annee: string;  // Backend uses 'annee' not 'nom'
  is_active: boolean;
}

interface ClassFormData {
  nom: string;
  niveau: string;
  annee_scolaire_id: number;
}

export default function ClassesManagement() {
  const [classes, setClasses] = useState<Class[]>([]);
  const [schoolYears, setSchoolYears] = useState<SchoolYear[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingClass, setEditingClass] = useState<Class | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formData, setFormData] = useState<ClassFormData>({
    nom: '',
    niveau: '',
    annee_scolaire_id: 0
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const { user } = useAuth();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError('');
    try {
      const [classesResponse, schoolYearsResponse] = await Promise.all([
        apiClient.get('/api/classes/'),
        apiClient.get('/api/school-years/')
      ]);

      const schoolYearsData = schoolYearsResponse.data;
      const classesData = classesResponse.data;

      // Map school year data to classes
      const classesWithSchoolYear = classesData.map((classe: any) => ({
        ...classe,
        annee_scolaire: schoolYearsData.find((sy: any) => sy.id === classe.annee_scolaire_id)
      }));

      setClasses(classesWithSchoolYear);
      setSchoolYears(schoolYearsData);
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const filteredClasses = classes.filter(classe => {
    const matchesSearch = searchTerm === '' ||
      classe.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classe.niveau.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const canEdit = user?.role === 'admin' || user?.role === 'secretary';

  const resetForm = () => {
    setFormData({
      nom: '',
      niveau: '',
      annee_scolaire_id: schoolYears.find(sy => sy.is_active)?.id || 0
    });
    setFormErrors({});
  };

  const handleAddNew = () => {
    if (!canEdit) return;
    setEditingClass(null);
    resetForm();
    setShowModal(true);
  };

  const handleEdit = (classe: Class) => {
    if (!canEdit) return;
    setEditingClass(classe);
    setFormData({
      nom: classe.nom,
      niveau: classe.niveau,
      annee_scolaire_id: classe.annee_scolaire_id
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleView = (classe: Class) => {
    console.log(`Voir la fiche de la classe ${classe.nom}`);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Le nom de la classe est requis';
    }
    if (!formData.niveau.trim()) {
      errors.niveau = 'Le niveau est requis';
    }
    if (!formData.annee_scolaire_id) {
      errors.annee_scolaire_id = 'L\'année scolaire est requise';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setFormLoading(true);
    try {
      if (editingClass) {
        await apiClient.put(`/api/classes/${editingClass.id}/`, formData);
      } else {
        await apiClient.post('/api/classes/', formData);
      }

      setShowModal(false);
      loadData();
      resetForm();
    } catch (error: any) {
      console.error('Error saving class:', error);

      // Handle validation errors from backend
      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        console.log('Backend validation errors:', backendErrors);

        // Map backend errors to form errors
        const newFormErrors: Record<string, string> = {};

        if (backendErrors.nom) {
          newFormErrors.nom = Array.isArray(backendErrors.nom) ? backendErrors.nom[0] : backendErrors.nom;
        }
        if (backendErrors.niveau) {
          newFormErrors.niveau = Array.isArray(backendErrors.niveau) ? backendErrors.niveau[0] : backendErrors.niveau;
        }

        if (backendErrors.annee_scolaire_id) {
          newFormErrors.annee_scolaire_id = Array.isArray(backendErrors.annee_scolaire_id) ? backendErrors.annee_scolaire_id[0] : backendErrors.annee_scolaire_id;
        }

        // If we have specific field errors, show them
        if (Object.keys(newFormErrors).length > 0) {
          setFormErrors(newFormErrors);
        } else {
          // Generic validation error
          setError(`Erreur de validation: ${JSON.stringify(backendErrors)}`);
        }
      } else {
        setError('Erreur lors de la sauvegarde');
      }
    } finally {
      setFormLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!canEdit || !confirm('Êtes-vous sûr de vouloir supprimer cette classe ?')) return;

    try {
      await apiClient.delete(`/api/classes/${id}/`);
      loadData();
    } catch (error) {
      console.error('Error deleting class:', error);
      setError('Erreur lors de la suppression');
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Classes</h1>
          <p className="text-gray-600 mt-1">
            {canEdit ? 'Management des classes de l\'établissement' : 'Consultation des classes de l\'établissement'}
          </p>
        </div>
        {canEdit && (
          <button
            onClick={handleAddNew}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
            style={{ backgroundColor: '#0a1186' }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle Classe
          </button>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={loadData}
            className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
          >
            Réessayer
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-3 text-gray-600">Chargement des classes...</span>
        </div>
      ) : (
        <>
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Classes</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{classes.length}</p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
                  <Building className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Classes Actives</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {classes.length}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
                  <Building className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Années Scolaires</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {schoolYears.length}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
                  <Users className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Niveaux</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {new Set(classes.map(c => (c.niveau || '').trim()).filter(Boolean)).size}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
                  <Filter className="h-6 w-6 text-green-700" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher une classe..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <select
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled
              >
                <option>Toutes les classes</option>
              </select>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Filter className="h-4 w-4 mr-2" />
                Plus de filtres
              </button>
            </div>
          </div>

          {/* Classes Table */}
          {filteredClasses.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-12 text-center">
              <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune classe trouvée</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm
                  ? 'Aucune classe ne correspond aux critères de recherche.'
                  : 'Aucune classe n\'est encore configurée.'
                }
              </p>
              {canEdit && !searchTerm && (
                <button
                  onClick={handleAddNew}
                  className="inline-flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter la première classe
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead style={{ backgroundColor: '#0a1186' }}>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Classe
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Niveau
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Année Scolaire
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredClasses.map((classe) => (
                      <tr key={classe.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <Building className="h-5 w-5 text-gray-600" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {classe.nom}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {classe.id}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            {classe.niveau}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {classe.annee_scolaire?.annee || 'Non assignée'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleView(classe)}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            {canEdit && (
                              <>
                                <button
                                  onClick={() => handleEdit(classe)}
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete(classe.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}

      {/* Modal Form */}
      {showModal && canEdit && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
            <div className="flex justify-between items-start mb-6 pb-4 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-1">
                  {editingClass ? 'Modifier la classe' : 'Nouvelle classe'}
                </h2>
                <p className="text-sm text-gray-600">
                  {editingClass ? 'Modifiez les informations de la classe' : 'Créez une nouvelle classe pour l\'établissement'}
                </p>
              </div>
              <button
                onClick={() => setShowModal(false)}
                className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
                type="button"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Nom de la classe */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nom de la classe *
                  </label>
                  <input
                    type="text"
                    value={formData.nom}
                    onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${formErrors.nom
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                      }`}
                    style={{ backgroundColor: '#fafafa' }}
                    placeholder="Ex: 6ème A"
                  />
                  {formErrors.nom && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.nom}</p>
                  )}
                </div>

                {/* Niveau */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Niveau *
                  </label>
                  <select
                    value={formData.niveau}
                    onChange={(e) => setFormData({ ...formData, niveau: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${formErrors.niveau
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                      }`}
                    style={{ backgroundColor: '#fafafa' }}
                  >
                    <option value="">Sélectionner un niveau</option>
                    <option value="6ème">6ème</option>
                    <option value="5ème">5ème</option>
                    <option value="4ème">4ème</option>
                    <option value="3ème">3ème</option>
                    <option value="2nde">2nde</option>
                    <option value="1ère">1ère</option>
                    <option value="Terminale">Terminale</option>
                  </select>
                  {formErrors.niveau && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.niveau}</p>
                  )}
                </div>
              </div>



              {/* Année scolaire */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Année scolaire *
                </label>
                <select
                  value={formData.annee_scolaire_id}
                  onChange={(e) => setFormData({ ...formData, annee_scolaire_id: parseInt(e.target.value) })}
                  className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${formErrors.annee_scolaire_id
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                    }`}
                  style={{ backgroundColor: '#fafafa' }}
                >
                  <option value={0}>Sélectionner une année scolaire</option>
                  {schoolYears.map(schoolYear => (
                    <option key={schoolYear.id} value={schoolYear.id}>
                      {schoolYear.annee} {schoolYear.is_active ? '(Active)' : '(Inactive)'}
                    </option>
                  ))}
                </select>
                {formErrors.annee_scolaire_id && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.annee_scolaire_id}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Sélectionnez l'année scolaire pour cette classe
                </p>
              </div>

              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200"
                  disabled={formLoading}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 hover:opacity-90 disabled:opacity-50"
                  style={{ backgroundColor: '#0a1186', focusRingColor: '#0a1186' }}
                >
                  {formLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Enregistrement...
                    </div>
                  ) : (
                    editingClass ? 'Modifier' : 'Créer'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div >
      )
      }
    </div >
  );
}
