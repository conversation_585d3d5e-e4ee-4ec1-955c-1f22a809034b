# schemas/attendance.py
from pydantic import BaseModel, validator
from datetime import date, datetime
from typing import Optional

class AttendanceBase(BaseModel):
    eleve_id: int
    cours_id: int
    date: Optional[date] = None
    statut: str
    commentaire: Optional[str] = None

    @validator('statut')
    def validate_statut(cls, v):
        valid_statuts = ['present', 'absent', 'late', 'excused']
        if v.lower() not in valid_statuts:
            raise ValueError(f'Status must be one of: {", ".join(valid_statuts)}')
        return v.lower()

class AttendanceCreate(AttendanceBase):
    pass

class AttendanceUpdate(BaseModel):
    eleve_id: Optional[int] = None
    cours_id: Optional[int] = None
    date: Optional[date] = None
    statut: Optional[str] = None
    commentaire: Optional[str] = None

class AttendanceRead(AttendanceBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
