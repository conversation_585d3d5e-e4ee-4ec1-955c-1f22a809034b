import React, { useState } from 'react';
import { DollarSign, TrendingUp, TrendingDown, PieChart, Download, Calendar } from 'lucide-react';

export default function Finances() {
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  const [selectedYear, setSelectedYear] = useState('2024');

  const periods = [
    { value: 'monthly', label: 'Mensuel' },
    { value: 'quarterly', label: 'Trimestriel' },
    { value: 'yearly', label: 'Annuel' }
  ];

  const monthlyRevenues = [
    { month: 'Janvier', revenue: 2400000, expenses: 800000 },
    { month: 'Février', revenue: 2200000, expenses: 750000 },
    { month: 'Mars', revenue: 2600000, expenses: 850000 },
    { month: 'Avril', revenue: 2300000, expenses: 780000 },
    { month: 'Mai', revenue: 2500000, expenses: 820000 }
  ];

  const expenseCategories = [
    { category: 'Salaires', amount: 1500000, percentage: 65, color: '#0a1186' },
    { category: 'Infrastructure', amount: 300000, percentage: 13, color: '#ffdd5a' },
    { category: 'Matériel pédagogique', amount: 250000, percentage: 11, color: '#a1ecff' },
    { category: 'Utilities', amount: 180000, percentage: 8, color: '#e3ea9c' },
    { category: 'Autres', amount: 70000, percentage: 3, color: '#c9cfcf' }
  ];

  const totalRevenue = monthlyRevenues.reduce((sum, item) => sum + item.revenue, 0);
  const totalExpenses = monthlyRevenues.reduce((sum, item) => sum + item.expenses, 0);
  const netProfit = totalRevenue - totalExpenses;

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR').format(amount) + ' AR';
  };

  const handleExportReport = () => {
    console.log('Export du rapport financier');
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">États Financiers</h1>
          <p className="text-gray-600 mt-1">Suivi des revenus et dépenses de l'établissement</p>
        </div>
        <div className="flex space-x-3 mt-4 sm:mt-0">
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            {periods.map(period => (
              <option key={period.value} value={period.value}>{period.label}</option>
            ))}
          </select>
          <button
            onClick={handleExportReport}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90"
            style={{ backgroundColor: '#0a1186' }}
          >
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </button>
        </div>
      </div>

      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Revenus Totaux</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{formatAmount(totalRevenue)}</p>
              <div className="flex items-center mt-2 text-sm text-green-600">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span>+8.2% vs période précédente</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Dépenses Totales</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{formatAmount(totalExpenses)}</p>
              <div className="flex items-center mt-2 text-sm text-orange-600">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span>+2.1% vs période précédente</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <TrendingDown className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Bénéfice Net</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{formatAmount(netProfit)}</p>
              <div className="flex items-center mt-2 text-sm text-green-600">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span>+15.3% vs période précédente</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <PieChart className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Marge Bénéficiaire</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">
                {((netProfit / totalRevenue) * 100).toFixed(1)}%
              </p>
              <div className="flex items-center mt-2 text-sm text-green-600">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span>Objectif: 25%</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <Calendar className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Revenue vs Expenses Chart */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Évolution Revenus vs Dépenses</h3>
        <div className="space-y-4">
          {monthlyRevenues.map((item, index) => {
            const maxValue = Math.max(...monthlyRevenues.map(m => m.revenue));
            const revenuePercentage = (item.revenue / maxValue) * 100;
            const expensePercentage = (item.expenses / maxValue) * 100;

            return (
              <div key={index} className="space-y-2">
                <div className="flex justify-between text-sm font-medium text-gray-900">
                  <span>{item.month}</span>
                  <span>
                    Revenus: {formatAmount(item.revenue)} | 
                    Dépenses: {formatAmount(item.expenses)}
                  </span>
                </div>
                <div className="space-y-1">
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="h-3 rounded-full"
                      style={{
                        width: `${revenuePercentage}%`,
                        backgroundColor: '#0a1186'
                      }}
                    ></div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${expensePercentage}%`,
                        backgroundColor: '#ffdd5a'
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        <div className="mt-6 flex justify-center space-x-6">
          <div className="flex items-center">
            <div className="w-4 h-4 rounded mr-2" style={{ backgroundColor: '#0a1186' }}></div>
            <span className="text-sm text-gray-600">Revenus</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded mr-2" style={{ backgroundColor: '#ffdd5a' }}></div>
            <span className="text-sm text-gray-600">Dépenses</span>
          </div>
        </div>
      </div>

      {/* Expense Breakdown */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Répartition des Dépenses</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Pie Chart Simulation */}
          <div className="text-center">
            <div className="relative w-48 h-48 mx-auto mb-4">
              <div className="w-48 h-48 rounded-full" style={{ 
                background: `conic-gradient(
                  #0a1186 0deg ${expenseCategories[0].percentage * 3.6}deg,
                  #ffdd5a ${expenseCategories[0].percentage * 3.6}deg ${(expenseCategories[0].percentage + expenseCategories[1].percentage) * 3.6}deg,
                  #a1ecff ${(expenseCategories[0].percentage + expenseCategories[1].percentage) * 3.6}deg ${(expenseCategories[0].percentage + expenseCategories[1].percentage + expenseCategories[2].percentage) * 3.6}deg,
                  #e3ea9c ${(expenseCategories[0].percentage + expenseCategories[1].percentage + expenseCategories[2].percentage) * 3.6}deg ${(expenseCategories[0].percentage + expenseCategories[1].percentage + expenseCategories[2].percentage + expenseCategories[3].percentage) * 3.6}deg,
                  #c9cfcf ${(expenseCategories[0].percentage + expenseCategories[1].percentage + expenseCategories[2].percentage + expenseCategories[3].percentage) * 3.6}deg 360deg
                )`
              }}>
                <div className="absolute inset-4 bg-white rounded-full flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-lg font-bold text-gray-900">{formatAmount(totalExpenses)}</p>
                    <p className="text-sm text-gray-500">Total</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Legend */}
          <div className="space-y-3">
            {expenseCategories.map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div 
                    className="w-4 h-4 rounded mr-3"
                    style={{ backgroundColor: category.color }}
                  ></div>
                  <span className="text-sm font-medium text-gray-900">{category.category}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{formatAmount(category.amount)}</p>
                  <p className="text-xs text-gray-500">{category.percentage}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Financial Goals */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Objectifs Financiers</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-3">
              <div className="w-24 h-24 rounded-full border-4 border-gray-200">
                <div className="w-16 h-16 rounded-full border-4 border-green-500 m-1 flex items-center justify-center">
                  <span className="text-lg font-bold text-green-600">89%</span>
                </div>
              </div>
            </div>
            <p className="font-medium text-gray-900">Revenus Mensuels</p>
            <p className="text-sm text-gray-500">Objectif: 2.5M AR</p>
          </div>

          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-3">
              <div className="w-24 h-24 rounded-full border-4 border-gray-200">
                <div className="w-16 h-16 rounded-full border-4 border-blue-500 m-1 flex items-center justify-center">
                  <span className="text-lg font-bold text-blue-600">75%</span>
                </div>
              </div>
            </div>
            <p className="font-medium text-gray-900">Réduction Coûts</p>
            <p className="text-sm text-gray-500">Objectif: -10%</p>
          </div>

          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-3">
              <div className="w-24 h-24 rounded-full border-4 border-gray-200">
                <div className="w-16 h-16 rounded-full border-4 border-yellow-500 m-1 flex items-center justify-center">
                  <span className="text-lg font-bold text-yellow-600">92%</span>
                </div>
              </div>
            </div>
            <p className="font-medium text-gray-900">Taux de Recouvrement</p>
            <p className="text-sm text-gray-500">Objectif: 95%</p>
          </div>
        </div>
      </div>
    </div>
  );
}