# models/teacher.py
from sqlalchemy import Column, Integer, String, Date, DateTime, Boolean
from datetime import datetime, date
from db import Base
from sqlalchemy.orm import relationship

class Teacher(Base):
    __tablename__ = "teachers"

    id = Column(Integer, primary_key=True, index=True)
    nom = Column(String, nullable=False, index=True)
    prenom = Column(String, nullable=False, index=True)
    specialite = Column(String, nullable=True)  # Main subject specialty
    email = Column(String, unique=True, nullable=True, index=True)
    telephone = Column(String, nullable=True)
    date_embauche = Column(Date, nullable=True)  # Hire date
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    courses = relationship("Course", back_populates="teacher")
    timetables = relationship("Timetable", back_populates="teacher")
