import React from 'react';
import { DollarSign, TrendingUp, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';

export default function FinancialDashboard() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Tableau de Bord Financier</h1>
          <p className="mt-2 text-gray-600">
            Vue d'ensemble des finances de l'établissement
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center">
            <PieChart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Tableau de Bord Financier
            </h2>
            <p className="text-gray-600 mb-6">
              <PERSON>tte fonctionnalité sera bientôt disponible. Elle permettra de :
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto text-left">
              <div className="flex items-start space-x-3">
                <DollarSign className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Revenus totaux</h3>
                  <p className="text-sm text-gray-600">Suivi des encaissements</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Analyses de tendances</h3>
                  <p className="text-sm text-gray-600">Évolution des paiements</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CreditCard className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Paiements en attente</h3>
                  <p className="text-sm text-gray-600">Suivi des impayés</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <PieChart className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Répartition par type</h3>
                  <p className="text-sm text-gray-600">Analyse des sources de revenus</p>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <p className="text-sm text-gray-500">
                En attendant, vous pouvez utiliser l'API backend directement : 
                <code className="bg-gray-100 px-2 py-1 rounded text-xs ml-1">/api/payments/</code>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
