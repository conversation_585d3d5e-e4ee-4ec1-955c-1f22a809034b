# crud/course.py
from typing import List
from sqlalchemy.orm import Session
from crud.base import CRUDBase
from models.course import Course
from schemas.course import CourseCreate, CourseUpdate

class CRUDCourse(CRUDBase[Course, CourseCreate, CourseUpdate]):
    def get_by_teacher(self, db: Session, *, enseignant_id: int) -> List[Course]:
        return db.query(Course).filter(Course.enseignant_id == enseignant_id).all()
    
    def get_by_class(self, db: Session, *, classe_id: int) -> List[Course]:
        return db.query(Course).filter(Course.classe_id == classe_id).all()
    
    def get_by_subject(self, db: Session, *, matiere_id: int) -> List[Course]:
        return db.query(Course).filter(Course.matiere_id == matiere_id).all()
    
    def get_by_teacher_and_class(
        self, db: Session, *, enseignant_id: int, classe_id: int
    ) -> List[Course]:
        return db.query(Course).filter(
            Course.enseignant_id == enseignant_id,
            Course.classe_id == classe_id
        ).all()

course = CRUDCourse(Course)
