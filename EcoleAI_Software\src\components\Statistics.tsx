import React, { useState } from 'react';
import { BarChart3, TrendingUp, Users, Award, PieChart, Target } from 'lucide-react';

export default function Statistics() {
  const [selectedPeriod, setSelectedPeriod] = useState('trimestre1');
  const [selectedClass, setSelectedClass] = useState('all');

  const periods = [
    { value: 'trimestre1', label: '1er Trimestre 2024' },
    { value: 'trimestre2', label: '2ème Trimestre 2024' },
    { value: 'trimestre3', label: '3ème Trimestre 2024' }
  ];

  const classes = [
    { value: 'all', label: 'Toutes les classes' },
    { value: '6A', label: '6ème A' },
    { value: '6B', label: '6ème B' },
    { value: '5A', label: '5ème A' }
  ];

  const successRates = [
    { class: '6ème A', rate: 92.3, students: 28, color: '#0a1186' },
    { class: '6ème B', rate: 87.5, students: 25, color: '#ffdd5a' },
    { class: '5ème A', rate: 94.1, students: 27, color: '#a1ecff' },
    { class: '5ème B', rate: 89.2, students: 24, color: '#e3ea9c' }
  ];

  const topStudents = [
    { rank: 1, name: 'RASOLONDRAIBE Solo', class: '6ème A', average: 18.5 },
    { rank: 2, name: 'RAMANANDRAIBE Vonjy', class: '5ème A', average: 18.2 },
    { rank: 3, name: 'RAKOTO Paul', class: '6ème B', average: 17.8 },
    { rank: 4, name: 'RAVELOJAONA Marie', class: 'CM2 A', average: 17.5 },
    { rank: 5, name: 'RAVELOSON Toky', class: '5ème B', average: 17.2 }
  ];

  const subjectStats = [
    { subject: 'Mathématiques', average: 14.2, passRate: 78, color: '#0a1186' },
    { subject: 'Français', average: 13.8, passRate: 82, color: '#ffdd5a' },
    { subject: 'Sciences', average: 15.1, passRate: 85, color: '#a1ecff' },
    { subject: 'Histoire-Géo', average: 14.9, passRate: 88, color: '#e3ea9c' },
    { subject: 'Anglais', average: 13.5, passRate: 75, color: '#c9cfcf' }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Statistiques</h1>
          <p className="text-gray-600 mt-1">Analyse des performances et taux de réussite</p>
        </div>
        <div className="flex space-x-3 mt-4 sm:mt-0">
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            {periods.map(period => (
              <option key={period.value} value={period.value}>{period.label}</option>
            ))}
          </select>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
          >
            {classes.map(cls => (
              <option key={cls.value} value={cls.value}>{cls.label}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Taux de Réussite Global</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">89.2%</p>
              <div className="flex items-center mt-2 text-sm text-green-600">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span>+3.2% vs trim. précédent</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <Target className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Moyenne Générale</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">14.1/20</p>
              <div className="flex items-center mt-2 text-sm text-green-600">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span>+0.8 vs trim. précédent</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <BarChart3 className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Élèves Évalués</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">847</p>
              <div className="flex items-center mt-2 text-sm text-blue-600">
                <Users className="h-4 w-4 mr-1" />
                <span>24 classes</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <Users className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Mentions</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">156</p>
              <div className="flex items-center mt-2 text-sm text-yellow-600">
                <Award className="h-4 w-4 mr-1" />
                <span>18.4% des élèves</span>
              </div>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <Award className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Success Rates by Class */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-900 mb-6">Taux de Réussite par Classe</h3>
          <div className="space-y-4">
            {successRates.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div 
                    className="w-4 h-4 rounded mr-3"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-sm font-medium text-gray-900">{item.class}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${item.rate}%`,
                        backgroundColor: item.color
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12 text-right">
                    {item.rate}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Students */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-900 mb-6">Top 5 Élèves</h3>
          <div className="space-y-4">
            {topStudents.map((student) => (
              <div key={student.rank} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium text-white mr-3 ${
                    student.rank === 1 ? 'bg-yellow-500' :
                    student.rank === 2 ? 'bg-gray-400' :
                    student.rank === 3 ? 'bg-orange-500' :
                    'bg-blue-500'
                  }`}>
                    {student.rank}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{student.name}</p>
                    <p className="text-xs text-gray-500">{student.class}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-gray-900">{student.average}/20</p>
                  <p className={`text-xs ${
                    student.average >= 18 ? 'text-green-600' :
                    student.average >= 16 ? 'text-blue-600' :
                    student.average >= 14 ? 'text-yellow-600' :
                    'text-gray-600'
                  }`}>
                    {student.average >= 18 ? 'Excellent' :
                     student.average >= 16 ? 'Très bien' :
                     student.average >= 14 ? 'Bien' :
                     'Assez bien'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Subject Statistics */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Performances par Matière</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {subjectStats.map((subject, index) => (
            <div key={index} className="text-center">
              <div 
                className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-3"
                style={{ backgroundColor: subject.color }}
              >
                <span className="text-white font-bold text-lg">{subject.average}</span>
              </div>
              <p className="text-sm font-medium text-gray-900">{subject.subject}</p>
              <p className="text-xs text-gray-500">Moyenne: {subject.average}/20</p>
              <p className="text-xs text-gray-500">Réussite: {subject.passRate}%</p>
              <div className="mt-2 w-full bg-gray-200 rounded-full h-1">
                <div
                  className="h-1 rounded-full"
                  style={{
                    width: `${subject.passRate}%`,
                    backgroundColor: subject.color
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Distribution Chart */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Répartition des Notes</h3>
        <div className="grid grid-cols-4 gap-6">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto rounded-full bg-green-500 flex items-center justify-center text-white font-bold text-lg mb-3">
              18%
            </div>
            <p className="text-sm font-medium text-gray-900">Excellent</p>
            <p className="text-xs text-gray-500">16-20</p>
          </div>
          <div className="text-center">
            <div className="w-20 h-20 mx-auto rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-lg mb-3">
              35%
            </div>
            <p className="text-sm font-medium text-gray-900">Bien</p>
            <p className="text-xs text-gray-500">14-16</p>
          </div>
          <div className="text-center">
            <div className="w-20 h-20 mx-auto rounded-full bg-yellow-500 flex items-center justify-center text-white font-bold text-lg mb-3">
              28%
            </div>
            <p className="text-sm font-medium text-gray-900">Passable</p>
            <p className="text-xs text-gray-500">10-14</p>
          </div>
          <div className="text-center">
            <div className="w-20 h-20 mx-auto rounded-full bg-red-500 flex items-center justify-center text-white font-bold text-lg mb-3">
              19%
            </div>
            <p className="text-sm font-medium text-gray-900">Insuffisant</p>
            <p className="text-xs text-gray-500">0-10</p>
          </div>
        </div>
      </div>
    </div>
  );
}