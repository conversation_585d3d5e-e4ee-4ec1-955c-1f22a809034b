# services/financial_service.py
from typing import List, Dict, Optional
from datetime import date, datetime, timedelta
from sqlalchemy.orm import Session
from crud.payment import payment
from crud.student import student
from models.payment import Payment

class FinancialService:
    @staticmethod
    def generate_payment_reference(db: Session) -> str:
        """Generate a unique payment reference"""
        today = datetime.now()
        date_prefix = today.strftime("%Y%m%d")
        
        # Count payments for today
        today_payments = db.query(Payment).filter(
            Payment.date_paiement == today.date()
        ).count()
        
        return f"PAY{date_prefix}{today_payments + 1:04d}"
    
    @staticmethod
    def get_financial_dashboard(db: Session, start_date: date = None, end_date: date = None) -> Dict:
        """Get comprehensive financial dashboard data"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        # Get payments in date range
        payments_in_range = payment.get_by_date_range(db, start_date=start_date, end_date=end_date)
        
        # Calculate totals by status
        total_paid = sum(p.montant for p in payments_in_range if p.statut == 'paid')
        total_pending = sum(p.montant for p in payments_in_range if p.statut == 'pending')
        total_partial = sum(p.montant for p in payments_in_range if p.statut == 'partial')
        
        # Group by payment reason
        payments_by_motif = {}
        for p in payments_in_range:
            if p.motif not in payments_by_motif:
                payments_by_motif[p.motif] = {"count": 0, "total": 0}
            payments_by_motif[p.motif]["count"] += 1
            payments_by_motif[p.motif]["total"] += p.montant
        
        # Get pending payments
        all_pending = payment.get_pending_payments(db)
        
        return {
            "period": {
                "start_date": start_date,
                "end_date": end_date
            },
            "totals": {
                "paid": total_paid,
                "pending": total_pending,
                "partial": total_partial,
                "total_revenue": total_paid + total_partial
            },
            "payments_by_reason": payments_by_motif,
            "pending_payments": {
                "count": len(all_pending),
                "total_amount": sum(p.montant for p in all_pending),
                "details": [
                    {
                        "payment_id": p.id,
                        "student_id": p.eleve_id,
                        "amount": p.montant,
                        "reason": p.motif,
                        "date": p.date_paiement
                    } for p in all_pending[:10]  # Limit to 10 most recent
                ]
            },
            "statistics": {
                "total_payments": len(payments_in_range),
                "average_payment": total_paid / len([p for p in payments_in_range if p.statut == 'paid']) if payments_in_range else 0
            }
        }
    
    @staticmethod
    def get_student_financial_status(db: Session, student_id: int) -> Dict:
        """Get detailed financial status for a specific student"""
        student_obj = student.get(db, student_id)
        if not student_obj:
            return {"error": "Student not found"}
        
        student_payments = payment.get_by_student(db, eleve_id=student_id)
        
        total_paid = sum(p.montant for p in student_payments if p.statut == 'paid')
        total_pending = sum(p.montant for p in student_payments if p.statut == 'pending')
        total_partial = sum(p.montant for p in student_payments if p.statut == 'partial')
        
        # Group payments by reason
        payments_by_reason = {}
        for p in student_payments:
            if p.motif not in payments_by_reason:
                payments_by_reason[p.motif] = []
            payments_by_reason[p.motif].append({
                "id": p.id,
                "amount": p.montant,
                "status": p.statut,
                "date": p.date_paiement,
                "reference": p.reference
            })
        
        # Determine overall status
        if total_pending > 0:
            status = "has_pending"
        elif total_partial > 0:
            status = "partial_payments"
        else:
            status = "up_to_date"
        
        return {
            "student": {
                "id": student_obj.id,
                "name": f"{student_obj.prenom} {student_obj.nom}",
                "matricule": student_obj.matricule
            },
            "financial_summary": {
                "total_paid": total_paid,
                "total_pending": total_pending,
                "total_partial": total_partial,
                "status": status
            },
            "payments_by_reason": payments_by_reason,
            "recent_payments": sorted(
                student_payments, 
                key=lambda x: x.date_paiement, 
                reverse=True
            )[:5]  # 5 most recent payments
        }
    
    @staticmethod
    def create_monthly_fees(db: Session, class_id: int, amount: float, month: str) -> List[Payment]:
        """Create monthly fee payments for all students in a class"""
        students_in_class = student.get_by_class(db, classe_id=class_id)
        created_payments = []
        
        for student_obj in students_in_class:
            if student_obj.is_active:
                payment_data = {
                    "eleve_id": student_obj.id,
                    "montant": amount,
                    "motif": "monthly_fee",
                    "statut": "pending",
                    "reference": FinancialService.generate_payment_reference(db),
                    "commentaire": f"Monthly fee for {month}"
                }
                new_payment = payment.create(db, obj_in=payment_data)
                created_payments.append(new_payment)
        
        return created_payments
