import React, { useState, useEffect } from 'react';
import { UserCheck, Plus, Search, Filter, Edit, Eye, Mail, Phone, Loader } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { apiClient } from '../config/api';

interface Teacher {
  id: number;
  nom: string;
  prenom: string;
  email?: string;
  telephone?: string;
  specialite?: string;
  date_embauche?: string;
  is_active: boolean;
  created_at?: string;
}

interface TeacherFormData {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  specialite: string;
  date_embauche: string;
  is_active: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export default function Teachers() {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [showModal, setShowModal] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formData, setFormData] = useState<TeacherFormData>({
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    specialite: '',
    date_embauche: '',
    is_active: true
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const { user } = useAuth();

  useEffect(() => {
    loadTeachers();
  }, []);

  const loadTeachers = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await apiClient.get('/api/teachers/');
      setTeachers(response.data);
    } catch (error) {
      console.error('Error loading teachers:', error);
      setError('Erreur lors du chargement des professeurs');
    } finally {
      setLoading(false);
    }
  };

  const filteredTeachers = teachers.filter(teacher => {
    const matchesSearch = searchTerm === '' ||
      teacher.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      teacher.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (teacher.email && teacher.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (teacher.specialite && teacher.specialite.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'active' && teacher.is_active) ||
      (statusFilter === 'inactive' && !teacher.is_active);

    return matchesSearch && matchesStatus;
  });

  const canEdit = user?.role === 'admin';

  const resetForm = () => {
    setFormData({
      nom: '',
      prenom: '',
      email: '',
      telephone: '',
      specialite: '',
      date_embauche: '',
      is_active: true
    });
    setFormErrors({});
  };

  const handleAddNew = () => {
    if (!canEdit) return;
    setEditingTeacher(null);
    resetForm();
    setShowModal(true);
  };

  const handleEdit = (teacher: Teacher) => {
    if (!canEdit) return;
    setEditingTeacher(teacher);
    setFormData({
      nom: teacher.nom,
      prenom: teacher.prenom,
      email: teacher.email || '',
      telephone: teacher.telephone || '',
      specialite: teacher.specialite || '',
      date_embauche: teacher.date_embauche || '',
      is_active: teacher.is_active
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleView = (teacher: Teacher) => {
    // Navigation vers la fiche détaillée du professeur
    console.log(`Voir la fiche de ${teacher.prenom} ${teacher.nom}`);
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Le nom est requis';
    }

    if (!formData.prenom.trim()) {
      errors.prenom = 'Le prénom est requis';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Format d\'email invalide';
    }

    if (formData.telephone && formData.telephone.length < 8) {
      errors.telephone = 'Le numéro de téléphone doit contenir au moins 8 caractères';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setFormLoading(true);
    try {
      // Prepare data for submission
      const submitData = {
        ...formData,
        email: formData.email || null,
        telephone: formData.telephone || null,
        specialite: formData.specialite || null,
        date_embauche: formData.date_embauche || null
      };

      if (editingTeacher) {
        // Update existing teacher
        await apiClient.put(`/api/teachers/${editingTeacher.id}`, submitData);
      } else {
        // Create new teacher
        await apiClient.post('/api/teachers/', submitData);
      }

      // Reload teachers and close modal
      await loadTeachers();
      setShowModal(false);
      resetForm();
    } catch (error: any) {
      console.error('Error saving teacher:', error);

      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        const newFormErrors: FormErrors = {};

        if (backendErrors.nom) {
          newFormErrors.nom = Array.isArray(backendErrors.nom) ? backendErrors.nom[0] : backendErrors.nom;
        }
        if (backendErrors.prenom) {
          newFormErrors.prenom = Array.isArray(backendErrors.prenom) ? backendErrors.prenom[0] : backendErrors.prenom;
        }
        if (backendErrors.email) {
          newFormErrors.email = Array.isArray(backendErrors.email) ? backendErrors.email[0] : backendErrors.email;
        }
        if (backendErrors.telephone) {
          newFormErrors.telephone = Array.isArray(backendErrors.telephone) ? backendErrors.telephone[0] : backendErrors.telephone;
        }

        setFormErrors(newFormErrors);
      } else {
        setFormErrors({ general: 'Erreur lors de l\'enregistrement du professeur' });
      }
    } finally {
      setFormLoading(false);
    }
  };



  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Professeurs</h1>
          <p className="text-gray-600 mt-1">
            {canEdit ? 'Management du corps enseignant' : 'Consultation du corps enseignant'}
          </p>
        </div>
        {canEdit && (
          <button
            onClick={handleAddNew}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
            style={{ backgroundColor: '#0a1186' }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Professeur
          </button>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={loadTeachers}
            className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
          >
            Réessayer
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-3 text-gray-600">Chargement des professeurs...</span>
        </div>
      ) : (
        <>
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Professeurs</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{teachers.length}</p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
                  <UserCheck className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Professeurs Actifs</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {teachers.filter(t => t.is_active).length}
                  </p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
                  <UserCheck className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Nouvelles Recrues</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">2</p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
                  <Plus className="h-6 w-6" style={{ color: '#0a1186' }} />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Matières Enseignées</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{new Set(teachers.map(t => (t.specialite || '').trim()).filter(Boolean)).size}</p>
                </div>
                <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
                  <Filter className="h-6 w-6 text-green-700" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher un professeur..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <select
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
              >
                <option value="all">Tous les statuts</option>
                <option value="active">Actifs</option>
                <option value="inactive">Inactifs</option>
              </select>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Filter className="h-4 w-4 mr-2" />
                Plus de filtres
              </button>
            </div>
          </div>

          {/* Teachers Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead style={{ backgroundColor: '#0a1186' }}>
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Professeur
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Spécialité
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTeachers.map((teacher) => (
                    <tr key={teacher.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {teacher.prenom[0]}{teacher.nom[0]}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {teacher.prenom} {teacher.nom}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {teacher.id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {teacher.specialite ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                            style={{ backgroundColor: '#ffdd5a', color: '#0a1186' }}>
                            {teacher.specialite}
                          </span>
                        ) : (
                          <span className="text-gray-400 text-sm">Non spécifiée</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {teacher.email && (
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4" />
                            <span>{teacher.email}</span>
                          </div>
                        )}
                        {teacher.telephone && (
                          <div className="flex items-center space-x-2 mt-1">
                            <Phone className="h-4 w-4" />
                            <span>{teacher.telephone}</span>
                          </div>
                        )}
                        {!teacher.email && !teacher.telephone && (
                          <span className="text-gray-400">Non renseigné</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${teacher.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                          {teacher.is_active ? 'Actif' : 'Inactif'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleView(teacher)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded"
                            title="Voir la fiche"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          {canEdit && (
                            <button
                              onClick={() => handleEdit(teacher)}
                              className="text-yellow-600 hover:text-yellow-900 p-1 rounded"
                              title="Modifier"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Empty State */}
              {filteredTeachers.length === 0 && !loading && (
                <div className="text-center py-12">
                  <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun professeur trouvé</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm || statusFilter !== 'all'
                      ? 'Aucun professeur ne correspond aux critères de recherche.'
                      : 'Aucun professeur n\'est encore enregistré dans le système.'
                    }
                  </p>
                  {canEdit && (
                    <button
                      onClick={() => setShowModal(true)}
                      className="inline-flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors"
                      style={{ backgroundColor: '#0a1186' }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter le premier professeur
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* Modal pour ajouter/modifier un professeur */}
      {showModal && canEdit && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">
                {editingTeacher ? 'Modifier le professeur' : 'Nouveau professeur'}
              </h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {formErrors.general && (
                <div className="p-3 text-red-700 bg-red-100 border border-red-300 rounded-md">
                  <span className="text-sm">{formErrors.general}</span>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Prénom *</label>
                  <input
                    type="text"
                    value={formData.prenom}
                    onChange={(e) => setFormData({ ...formData, prenom: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.prenom ? 'border-red-500' : 'border-gray-300'
                      }`}
                    placeholder="Prénom du professeur"
                  />
                  {formErrors.prenom && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.prenom}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nom *</label>
                  <input
                    type="text"
                    value={formData.nom}
                    onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.nom ? 'border-red-500' : 'border-gray-300'
                      }`}
                    placeholder="Nom du professeur"
                  />
                  {formErrors.nom && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.nom}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.email ? 'border-red-500' : 'border-gray-300'
                      }`}
                    placeholder="<EMAIL>"
                  />
                  {formErrors.email && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Téléphone</label>
                  <input
                    type="tel"
                    value={formData.telephone}
                    onChange={(e) => setFormData({ ...formData, telephone: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.telephone ? 'border-red-500' : 'border-gray-300'
                      }`}
                    placeholder="Numéro de téléphone"
                  />
                  {formErrors.telephone && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.telephone}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Spécialité</label>
                <input
                  type="text"
                  value={formData.specialite}
                  onChange={(e) => setFormData({ ...formData, specialite: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: Mathématiques, Français, Sciences..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date d'embauche</label>
                  <input
                    type="date"
                    value={formData.date_embauche}
                    onChange={(e) => setFormData({ ...formData, date_embauche: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.is_active}
                      onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">Professeur actif</span>
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-6">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                  disabled={formLoading}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 disabled:opacity-50"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  {formLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {editingTeacher ? 'Modification...' : 'Création...'}
                    </div>
                  ) : (
                    editingTeacher ? 'Modifier' : 'Ajouter'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}