// Define user data interface for type safety
interface UserData {
    id: number;
    username: string;
    email?: string;
    full_name?: string;
    phone?: string;
    role?: string;
    disabled?: boolean;
    created_at?: string;
}

export class SecureTokenStorage {
    private static readonly ACCESS_TOKEN_KEY = 'ecole_ai_access_token';
    private static readonly REFRESH_TOKEN_KEY = 'ecole_ai_refresh_token';
    private static readonly USER_DATA_KEY = 'ecole-ai-user';

    static setTokens(accessToken: string, refreshToken: string): void {
        try {
            const tokenData = {
                token: accessToken,
                timestamp: Date.now()
            };

            localStorage.setItem(this.ACCESS_TOKEN_KEY, JSON.stringify(tokenData));
            localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
        } catch (error) {
            console.error('Failed to store tokens:', error);
            throw new Error('Token storage failed');
        }
    }

    static getAccessToken(): string | null {
        try {
            const tokenData = localStorage.getItem(this.ACCESS_TOKEN_KEY);
            if (!tokenData) return null;

            const parsed = JSON.parse(tokenData);

            if (!parsed.token || !parsed.timestamp) {
                this.clearTokens();
                return null;
            }

            const tokenAge = Date.now() - parsed.timestamp;
            const maxAge = 24 * 60 * 60 * 1000;

            if (tokenAge > maxAge) {
                this.clearTokens();
                return null;
            }

            return parsed.token;
        } catch (error) {
            console.error('Failed to retrieve access token:', error);
            this.clearTokens();
            return null;
        }
    }

    static getRefreshToken(): string | null {
        try {
            return localStorage.getItem(this.REFRESH_TOKEN_KEY);
        } catch (error) {
            console.error('Failed to retrieve refresh token:', error);
            return null;
        }
    }

    static clearTokens(): void {
        try {
            localStorage.removeItem(this.ACCESS_TOKEN_KEY);
            localStorage.removeItem(this.REFRESH_TOKEN_KEY);
            localStorage.removeItem(this.USER_DATA_KEY);
        } catch (error) {
            console.error('Failed to clear tokens:', error);
        }
    }

    static hasValidTokens(): boolean {
        const accessToken = this.getAccessToken();
        const refreshToken = this.getRefreshToken();
        return !!(accessToken && refreshToken);
    }

    static setUserData(userData: UserData): void {
        try {
            localStorage.setItem(this.USER_DATA_KEY, JSON.stringify(userData));
        } catch (error) {
            console.error('Failed to store user data:', error);
        }
    }

    static getUserData(): UserData | null {
        try {
            const userData = localStorage.getItem(this.USER_DATA_KEY);
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Failed to retrieve user data:', error);
            return null;
        }
    }

    static isValidTokenFormat(token: string): boolean {
        try {
            const parts = token.split('.');
            return parts.length === 3;
        } catch {
            return false;
        }
    }

    static getTokenExpiration(token: string): Date | null {
        try {
            if (!this.isValidTokenFormat(token)) return null;

            const payload = JSON.parse(atob(token.split('.')[1]));
            return payload.exp ? new Date(payload.exp * 1000) : null;
        } catch (error) {
            console.error('Failed to parse token expiration:', error);
            return null;
        }
    }

    static isTokenExpired(token: string): boolean {
        const expiration = this.getTokenExpiration(token);
        return expiration ? expiration.getTime() < Date.now() : true;
    }
}