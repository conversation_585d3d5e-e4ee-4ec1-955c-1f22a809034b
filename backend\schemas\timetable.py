# schemas/timetable.py
from pydantic import BaseModel, validator
from datetime import date, time, datetime
from typing import Optional
from enum import Enum

class DayOfWeekEnum(str, Enum):
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"

class TimetableBase(BaseModel):
    classe_id: int
    matiere_id: int
    enseignant_id: int
    annee_scolaire_id: int
    jour_semaine: DayOfWeekEnum
    heure_debut: time
    heure_fin: time
    salle: Optional[str] = None
    description: Optional[str] = None
    date_debut: Optional[date] = None
    date_fin: Optional[date] = None
    is_active: Optional[bool] = True

    @validator('heure_fin')
    def validate_time_order(cls, v, values):
        if 'heure_debut' in values and v <= values['heure_debut']:
            raise ValueError('End time must be after start time')
        return v

    @validator('date_fin')
    def validate_date_order(cls, v, values):
        if v and 'date_debut' in values and values['date_debut'] and v <= values['date_debut']:
            raise ValueError('End date must be after start date')
        return v

    @validator('salle')
    def validate_salle(cls, v):
        if v and len(v.strip()) < 1:
            raise ValueError('Room name cannot be empty')
        return v.strip() if v else None

class TimetableCreate(TimetableBase):
    pass

class TimetableUpdate(BaseModel):
    classe_id: Optional[int] = None
    matiere_id: Optional[int] = None
    enseignant_id: Optional[int] = None
    annee_scolaire_id: Optional[int] = None
    jour_semaine: Optional[DayOfWeekEnum] = None
    heure_debut: Optional[time] = None
    heure_fin: Optional[time] = None
    salle: Optional[str] = None
    description: Optional[str] = None
    date_debut: Optional[date] = None
    date_fin: Optional[date] = None
    is_active: Optional[bool] = None

class TimetableRead(TimetableBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Extended schemas with relationship data
class TimetableWithDetails(TimetableRead):
    classe: Optional[dict] = None
    subject: Optional[dict] = None
    teacher: Optional[dict] = None
    school_year: Optional[dict] = None

    class Config:
        from_attributes = True
