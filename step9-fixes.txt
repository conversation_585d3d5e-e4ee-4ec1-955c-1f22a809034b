# Step 9 Implementation Fixes and Missing Components

## Issues Found After Step 9 Implementation

### ❌ **Critical Issues Identified:**

1. **Missing React Import**: SessionWarning.tsx missing React import
2. **Missing Testing Utilities**: authTest.ts file not created
3. **Missing Development Tools**: AuthTester component and dev directory not created
4. **Type Issues**: SessionInfo interface uses `any` type for user
5. **Session Warning Logic**: Potential infinite re-render in useSessionWarning
6. **Missing Token Refresh**: "Stay Logged In" button doesn't actually refresh tokens
7. **AuthService Logout**: Still uses old localStorage methods

## Required Fixes

### 1. Fix SessionWarning Component

#### File: `src/components/SessionWarning.tsx`
**Action**: Replace the entire file content with:

```typescript
import React from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { useSessionWarning } from '../hooks/useSessionWarning';
import { useAuth } from '../contexts/AuthContext';
import { AuthService } from '../services/authService';
import { SecureTokenStorage } from '../utils/tokenStorage';

export default function SessionWarning() {
  const { logout } = useAuth();
  const { showWarning, timeLeft, dismissWarning, formatTimeLeft } = useSessionWarning({
    warningTime: 5 * 60 * 1000, // 5 minutes
    onExpired: () => {
      console.log('Session expired');
    }
  });

  if (!showWarning || !timeLeft) {
    return null;
  }

  const handleExtendSession = async () => {
    try {
      // Try to refresh the token to extend session
      const refreshToken = SecureTokenStorage.getRefreshToken();
      if (refreshToken) {
        const authResponse = await AuthService.refreshToken(refreshToken);
        SecureTokenStorage.setTokens(authResponse.access_token, authResponse.refresh_token);
        console.log('Session extended successfully');
      }
      dismissWarning();
    } catch (error) {
      console.error('Failed to extend session:', error);
      // If refresh fails, logout
      logout();
    }
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4 max-w-sm">
      <div className="flex items-start">
        <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            Session Expiring Soon
          </h3>
          <p className="mt-1 text-sm text-yellow-700">
            Your session will expire in {formatTimeLeft(timeLeft)}
          </p>
          <div className="mt-3 flex space-x-2">
            <button
              onClick={handleExtendSession}
              className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded text-sm font-medium"
            >
              Stay Logged In
            </button>
            <button
              onClick={handleLogout}
              className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded text-sm font-medium"
            >
              Logout
            </button>
          </div>
        </div>
        <button
          onClick={dismissWarning}
          className="ml-2 text-yellow-400 hover:text-yellow-600"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
```

### 2. Fix useSession Hook Type Issues

#### File: `src/hooks/useSession.ts`
**Action**: Replace lines 1-13 with:

```typescript
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { SecureTokenStorage } from '../utils/tokenStorage';
import { envConfig } from '../config/environment';
import { User } from '../services/authService';

export interface SessionInfo {
  user: User | null;
  isAuthenticated: boolean;
  sessionExpiry: Date | null;
  timeUntilExpiry: number | null;
  isExpiringSoon: boolean;
  sessionDuration: number | null;
}
```

### 3. Fix useSessionWarning Infinite Re-render

#### File: `src/hooks/useSessionWarning.ts`
**Action**: Replace lines 44-57 with:

```typescript
    // Update every second when warning is shown
    if (showWarning && timeUntilExpiry > 0) {
      const interval = setInterval(() => {
        const currentTimeLeft = sessionExpiry ? sessionExpiry.getTime() - Date.now() : 0;
        if (currentTimeLeft <= 0) {
          setShowWarning(false);
          setTimeLeft(null);
          onExpired?.();
        } else {
          setTimeLeft(currentTimeLeft);
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [timeUntilExpiry, isAuthenticated, warningTime, showWarning, onWarning, onExpired]);
```

**Action**: Add missing import at the top:

```typescript
import { useState, useEffect, useCallback } from 'react';
import { useSession } from './useSession';

interface UseSessionWarningOptions {
  warningTime?: number;
  onWarning?: (timeLeft: number) => void;
  onExpired?: () => void;
}

export function useSessionWarning(options: UseSessionWarningOptions = {}) {
  const { warningTime = 5 * 60 * 1000, onWarning, onExpired } = options;
  const { timeUntilExpiry, isAuthenticated, sessionExpiry } = useSession(); // Add sessionExpiry
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
```

### 4. Create Missing Authentication Test Utilities

#### File: `src/utils/authTest.ts` (NEW FILE)
**Action**: Create this new file:

```typescript
import { AuthService } from '../services/authService';
import { SecureTokenStorage } from './tokenStorage';

export interface AuthTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

export class AuthTestUtils {
  /**
   * Test complete authentication flow
   */
  static async testAuthFlow(): Promise<AuthTestResult[]> {
    const results: AuthTestResult[] = [];

    try {
      // Test 1: Login
      console.log('Testing login...');
      const loginResult = await AuthService.login({
        username: 'admin',
        password: 'admin123'
      });
      
      results.push({
        success: true,
        message: 'Login successful',
        data: { hasTokens: !!(loginResult.access_token && loginResult.refresh_token) }
      });

      // Test 2: Get user profile
      console.log('Testing user profile fetch...');
      const user = await AuthService.getCurrentUser();
      
      results.push({
        success: true,
        message: 'User profile fetch successful',
        data: { userId: user.id, username: user.username }
      });

      // Test 3: Token refresh
      console.log('Testing token refresh...');
      const refreshResult = await AuthService.refreshToken(loginResult.refresh_token);
      
      results.push({
        success: true,
        message: 'Token refresh successful',
        data: { hasNewToken: !!refreshResult.access_token }
      });

      // Test 4: Token storage
      console.log('Testing secure token storage...');
      SecureTokenStorage.setTokens(refreshResult.access_token, refreshResult.refresh_token);
      const storedToken = SecureTokenStorage.getAccessToken();
      
      results.push({
        success: !!storedToken,
        message: storedToken ? 'Token storage successful' : 'Token storage failed',
        data: { tokenStored: !!storedToken }
      });

    } catch (error) {
      results.push({
        success: false,
        message: 'Authentication test failed',
        error: error
      });
    }

    return results;
  }

  /**
   * Test token validation
   */
  static testTokenValidation(): AuthTestResult {
    try {
      const token = SecureTokenStorage.getAccessToken();
      
      if (!token) {
        return {
          success: false,
          message: 'No token found'
        };
      }

      const isValid = SecureTokenStorage.isValidTokenFormat(token);
      const isExpired = SecureTokenStorage.isTokenExpired(token);
      const expiration = SecureTokenStorage.getTokenExpiration(token);

      return {
        success: isValid && !isExpired,
        message: `Token validation: ${isValid ? 'valid format' : 'invalid format'}, ${isExpired ? 'expired' : 'not expired'}`,
        data: {
          isValid,
          isExpired,
          expiration: expiration?.toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Token validation failed',
        error
      };
    }
  }

  /**
   * Test security features
   */
  static async testSecurityFeatures(username: string = 'testuser'): Promise<AuthTestResult[]> {
    const { SecurityUtils } = await import('./securityUtils');
    const results: AuthTestResult[] = [];

    try {
      // Test login attempt tracking
      SecurityUtils.recordLoginAttempt(username, false);
      const failedAttempts = SecurityUtils.getRecentFailedAttempts(username);
      
      results.push({
        success: failedAttempts > 0,
        message: `Login attempt tracking: ${failedAttempts} failed attempts recorded`,
        data: { failedAttempts }
      });

      // Test lockout functionality
      const isLockedOut = SecurityUtils.isUserLockedOut(username);
      
      results.push({
        success: true,
        message: `User lockout status: ${isLockedOut ? 'locked out' : 'not locked out'}`,
        data: { isLockedOut }
      });

      // Test password validation
      const passwordTest = SecurityUtils.validatePasswordStrength('TestPassword123!');
      
      results.push({
        success: passwordTest.isValid,
        message: `Password validation: ${passwordTest.isValid ? 'passed' : 'failed'}`,
        data: { score: passwordTest.score, errors: passwordTest.errors }
      });

    } catch (error) {
      results.push({
        success: false,
        message: 'Security features test failed',
        error
      });
    }

    return results;
  }

  /**
   * Run all tests
   */
  static async runAllTests(): Promise<void> {
    console.group('🔐 Authentication Tests');
    
    try {
      // Test authentication flow
      console.group('Authentication Flow Tests');
      const authResults = await this.testAuthFlow();
      authResults.forEach(result => {
        console.log(result.success ? '✅' : '❌', result.message, result.data || result.error);
      });
      console.groupEnd();

      // Test token validation
      console.group('Token Validation Tests');
      const tokenResult = this.testTokenValidation();
      console.log(tokenResult.success ? '✅' : '❌', tokenResult.message, tokenResult.data || tokenResult.error);
      console.groupEnd();

      // Test security features
      console.group('Security Features Tests');
      const securityResults = await this.testSecurityFeatures();
      securityResults.forEach(result => {
        console.log(result.success ? '✅' : '❌', result.message, result.data || result.error);
      });
      console.groupEnd();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
    
    console.groupEnd();
  }
}

// Export convenience functions
export const testAuthFlow = AuthTestUtils.testAuthFlow;
export const runAllAuthTests = AuthTestUtils.runAllTests;
```

### 5. Create Development Testing Component

#### Directory: `src/dev/` (NEW DIRECTORY)
**Action**: Create this directory first

#### File: `src/dev/AuthTester.tsx` (NEW FILE)
**Action**: Create this new file:

```typescript
import React from 'react';
import { runAllAuthTests } from '../utils/authTest';

export default function AuthTester() {
  const handleRunTests = async () => {
    await runAllAuthTests();
  };

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={handleRunTests}
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow-lg text-sm font-medium"
      >
        🧪 Run Auth Tests
      </button>
    </div>
  );
}
```

### 6. Fix AuthService Logout Method

#### File: `src/services/authService.ts`
**Action**: Replace the logout method (lines 66-70) with:

```typescript
static logout(): void {
  // Use SecureTokenStorage instead of direct localStorage
  import('../utils/tokenStorage').then(({ SecureTokenStorage }) => {
    SecureTokenStorage.clearTokens();
  }).catch(error => {
    console.error('Failed to clear tokens:', error);
    // Fallback to direct localStorage clearing
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('ecole-ai-user');
  });
}
```

### 7. Add AuthTester to Layout

#### File: `src/components/Layout.tsx`
**Action**: Add these imports at the top:

```typescript
import AuthTester from '../dev/AuthTester';
```

**Action**: Add the AuthTester component before the closing div in your Layout component:

```typescript
// Add this just before the closing </div> or </main> in your Layout component
<AuthTester />
```

### 8. Add Session Activity Tracking Hook

#### File: `src/hooks/useActivityTracker.ts` (NEW FILE)
**Action**: Create this new file:

```typescript
import { useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface ActivityData {
  lastActivity: Date;
  totalClicks: number;
  totalKeystrokes: number;
  sessionStart: Date;
}

export function useActivityTracker() {
  const { isAuthenticated } = useAuth();
  const activityRef = useRef<ActivityData>({
    lastActivity: new Date(),
    totalClicks: 0,
    totalKeystrokes: 0,
    sessionStart: new Date()
  });

  useEffect(() => {
    if (!isAuthenticated) return;

    const handleActivity = (type: 'click' | 'keypress') => {
      activityRef.current.lastActivity = new Date();

      if (type === 'click') {
        activityRef.current.totalClicks++;
      } else if (type === 'keypress') {
        activityRef.current.totalKeystrokes++;
      }
    };

    const handleClick = () => handleActivity('click');
    const handleKeypress = () => handleActivity('keypress');

    // Add event listeners
    document.addEventListener('click', handleClick);
    document.addEventListener('keypress', handleKeypress);

    return () => {
      document.removeEventListener('click', handleClick);
      document.removeEventListener('keypress', handleKeypress);
    };
  }, [isAuthenticated]);

  const getActivityStats = () => {
    const now = new Date();
    const sessionDuration = now.getTime() - activityRef.current.sessionStart.getTime();
    const timeSinceLastActivity = now.getTime() - activityRef.current.lastActivity.getTime();

    return {
      ...activityRef.current,
      sessionDuration,
      timeSinceLastActivity,
      isActive: timeSinceLastActivity < 30000 // Active if activity within 30 seconds
    };
  };

  return {
    getActivityStats,
    resetSession: () => {
      activityRef.current = {
        lastActivity: new Date(),
        totalClicks: 0,
        totalKeystrokes: 0,
        sessionStart: new Date()
      };
    }
  };
}
```

### 9. Enhanced Session Management Hook

#### File: `src/hooks/useEnhancedSession.ts` (NEW FILE)
**Action**: Create this new file:

```typescript
import { useSession } from './useSession';
import { useActivityTracker } from './useActivityTracker';
import { useAuth } from '../contexts/AuthContext';

export function useEnhancedSession() {
  const session = useSession();
  const { getActivityStats } = useActivityTracker();
  const { logout } = useAuth();

  const getSessionHealth = () => {
    const activity = getActivityStats();
    const { timeUntilExpiry, isExpiringSoon } = session;

    return {
      ...session,
      ...activity,
      healthScore: calculateHealthScore(session, activity),
      recommendations: getRecommendations(session, activity)
    };
  };

  const calculateHealthScore = (sessionData: any, activityData: any) => {
    let score = 100;

    // Deduct points for session age
    if (sessionData.sessionDuration > 2 * 60 * 60 * 1000) { // 2 hours
      score -= 20;
    }

    // Deduct points for inactivity
    if (activityData.timeSinceLastActivity > 10 * 60 * 1000) { // 10 minutes
      score -= 30;
    }

    // Deduct points for approaching expiry
    if (sessionData.isExpiringSoon) {
      score -= 25;
    }

    return Math.max(0, score);
  };

  const getRecommendations = (sessionData: any, activityData: any) => {
    const recommendations = [];

    if (sessionData.isExpiringSoon) {
      recommendations.push('Session expiring soon - consider refreshing');
    }

    if (activityData.timeSinceLastActivity > 15 * 60 * 1000) {
      recommendations.push('Long period of inactivity detected');
    }

    if (sessionData.sessionDuration > 3 * 60 * 60 * 1000) {
      recommendations.push('Long session - consider taking a break');
    }

    return recommendations;
  };

  return {
    ...session,
    getSessionHealth,
    forceLogout: logout
  };
}
```

### 10. Integration Instructions

#### Step 1: Update App.tsx to use enhanced session management
**File**: `src/App.tsx`
**Action**: Add import and use enhanced session:

```typescript
import { useEnhancedSession } from './hooks/useEnhancedSession';

// Inside AppRoutes function, add:
const sessionHealth = useEnhancedSession().getSessionHealth();

// Log session health in development
if (import.meta.env.DEV) {
  console.log('Session Health:', sessionHealth);
}
```

#### Step 2: Add environment variables for session management
**File**: `.env`
**Action**: Add these variables:

```env
VITE_SESSION_WARNING_TIME=300000
VITE_ACTIVITY_TIMEOUT=1800000
VITE_MAX_SESSION_DURATION=14400000
```

## Testing the Fixes

1. **Test Session Warning**: Login and wait for session warning popup
2. **Test Token Refresh**: Click "Stay Logged In" and verify token is refreshed
3. **Test Activity Tracking**: Check console for activity stats
4. **Test Authentication Flow**: Click "Run Auth Tests" button
5. **Test Session Health**: Check console for session health scores

## Benefits of These Fixes

1. **Proper Token Refresh**: "Stay Logged In" actually refreshes tokens
2. **Type Safety**: Proper TypeScript types throughout
3. **Activity Monitoring**: Comprehensive user activity tracking
4. **Session Health**: Real-time session quality assessment
5. **Development Tools**: Easy testing and debugging
6. **Performance**: Fixed infinite re-render issues
7. **Error Handling**: Robust error handling for all scenarios

These fixes ensure your Step 9 implementation is production-ready with comprehensive session management, proper error handling, and excellent developer experience.
```
