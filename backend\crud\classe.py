# crud/classe.py
from typing import Optional, List
from sqlalchemy.orm import Session
from crud.base import CRUDBase
from models.classe import Classe
from schemas.classe import ClasseCreate, ClasseUpdate

class CRUDClasse(CRUDBase[Classe, ClasseCreate, ClasseUpdate]):
    def get_by_name_and_year(
        self, db: Session, *, nom: str, annee_scolaire_id: int
    ) -> Optional[Classe]:
        return db.query(Classe).filter(
            Classe.nom == nom,
            Classe.annee_scolaire_id == annee_scolaire_id
        ).first()
    
    def get_by_school_year(self, db: Session, *, annee_scolaire_id: int) -> List[Classe]:
        return db.query(Classe).filter(Classe.annee_scolaire_id == annee_scolaire_id).all()
    
    def get_by_niveau(self, db: Session, *, niveau: str) -> List[Classe]:
        return db.query(Classe).filter(Classe.niveau == niveau).all()

classe_crud = CRUDClasse(Classe)
