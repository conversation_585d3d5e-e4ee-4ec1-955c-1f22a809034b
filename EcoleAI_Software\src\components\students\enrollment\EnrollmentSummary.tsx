import React, { useState, useEffect, useCallback } from 'react';
import {
  CheckCircle,
  User,
  GraduationCap,
  DollarSign,
  Calendar,
  Phone,
  MapPin,
  Hash,
  AlertCircle,
  Loader,
  FileText,
  Send
} from 'lucide-react';
import { apiClient } from '../../../config/api';

interface EnrollmentSummaryProps {
  enrollmentData: any;
  updateEnrollmentData: (section: string, data: any) => void;
  updateStepCompletion: (stepId: string, completed: boolean) => void;
  currentStep: any;
}

export default function EnrollmentSummary({
  enrollmentData,
  updateEnrollmentData,
  updateStepCompletion,
  currentStep
}: EnrollmentSummaryProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState<string>('');
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [createdStudentId, setCreatedStudentId] = useState<number | null>(null);

  useEffect(() => {
    // This step is always considered complete for review
    updateStepCompletion(currentStep.id, true);
  }, []);

  const handleFinalSubmission = async () => {
    setIsSubmitting(true);
    setSubmissionError('');

    try {
      // 1. Create the student
      const studentData = {
        matricule: enrollmentData.student.matricule,
        nom: enrollmentData.student.nom,
        prenom: enrollmentData.student.prenom,
        date_naissance: enrollmentData.student.date_naissance,
        sexe: enrollmentData.student.sexe,
        adresse: enrollmentData.student.adresse || '',
        nom_parent: enrollmentData.student.nom_parent,
        contact_parent: enrollmentData.student.contact_parent,
        classe_id: parseInt(enrollmentData.student.classe_id),
        is_active: true,
      };

      const studentResponse = await apiClient.post('/api/students/', studentData);
      const createdStudent = studentResponse.data;
      setCreatedStudentId(createdStudent.id);

      // 2. Create financial records if needed
      const financialPromises = [];

      // Registration fee payment
      if (enrollmentData.financial.registrationPaid) {
        const registrationPayment = {
          eleve_id: createdStudent.id,
          montant: enrollmentData.financial.registrationFee,
          motif: 'registration',
          statut: 'paid',
          methode_paiement: enrollmentData.financial.registrationMethod,
          commentaire: 'Frais d\'inscription payés lors de l\'inscription',
        };

        financialPromises.push(
          apiClient.post('/api/payments/', registrationPayment)
        );
      }

      // Additional fees
      enrollmentData.financial.additionalFees?.forEach((fee: any) => {
        const additionalPayment = {
          eleve_id: createdStudent.id,
          montant: fee.amount,
          motif: fee.type,
          statut: 'pending',
          commentaire: fee.description,
        };

        financialPromises.push(
          apiClient.post('/api/payments/', additionalPayment)
        );
      });

      // Monthly fee setup (create first month as pending)
      if (enrollmentData.financial.monthlyFeeSetup) {
        const monthlyPayment = {
          eleve_id: createdStudent.id,
          montant: enrollmentData.financial.monthlyFee,
          motif: 'monthly_fee',
          statut: 'pending',
          commentaire: 'Frais mensuel - Premier mois',
        };

        financialPromises.push(
          apiClient.post('/api/payments/', monthlyPayment)
        );
      }

      // Wait for all financial records to be created
      await Promise.all(financialPromises);

      setSubmissionSuccess(true);
      updateEnrollmentData('enrollment', {
        completed: true,
        studentId: createdStudent.id,
        completedAt: new Date().toISOString(),
      });

    } catch (error) {
      console.error('Error during final submission:', error);
      setSubmissionError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSelectedClass = useCallback(() => {
    return enrollmentData.prerequisites?.availableClasses?.find(
      (cls: any) => cls.id === parseInt(enrollmentData.student?.classe_id || '0')
    );
  }, [enrollmentData.prerequisites?.availableClasses, enrollmentData.student?.classe_id]);

  const getTotalInitialAmount = useCallback(() => {
    let total = 0;
    if (enrollmentData.financial?.registrationPaid) {
      total += enrollmentData.financial.registrationFee || 0;
    }
    if (enrollmentData.financial?.additionalFees) {
      total += enrollmentData.financial.additionalFees.reduce(
        (sum: number, fee: any) => sum + (fee.amount || 0), 0
      );
    }
    return total;
  }, [enrollmentData.financial?.registrationPaid, enrollmentData.financial?.registrationFee, enrollmentData.financial?.additionalFees]);

  if (submissionSuccess) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Inscription Réussie !
        </h2>
        <p className="text-gray-600 mb-6">
          L'élève {enrollmentData.student?.prenom} {enrollmentData.student?.nom} a été inscrit avec succès.
        </p>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 max-w-md mx-auto">
          <p className="text-sm text-green-800">
            <strong>Matricule:</strong> {enrollmentData.student?.matricule}
          </p>
          {createdStudentId && (
            <p className="text-sm text-green-800">
              <strong>ID Système:</strong> {createdStudentId}
            </p>
          )}
        </div>
        <div className="space-x-4">
          <button
            onClick={() => window.location.href = '/students/directory'}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Voir le Répertoire des Élèves
          </button>
          <button
            onClick={() => window.location.href = '/students/enrollment'}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Inscrire un Autre Élève
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Finalisation de l'Inscription</h2>
        <p className="text-sm text-gray-600 mt-1">
          Vérifiez toutes les informations avant de finaliser l'inscription
        </p>
      </div>

      {/* Student Information Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <User className="h-5 w-5 mr-2 text-blue-600" />
          Informations de l'Élève
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center">
              <Hash className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">Matricule:</span>
              <span className="ml-2 font-medium">{enrollmentData.student?.matricule}</span>
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">Nom complet:</span>
              <span className="ml-2 font-medium">
                {enrollmentData.student?.prenom} {enrollmentData.student?.nom}
              </span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">Date de naissance:</span>
              <span className="ml-2 font-medium">{enrollmentData.student?.date_naissance}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-600">Sexe:</span>
              <span className="ml-2 font-medium">
                {enrollmentData.student?.sexe === 'M' ? 'Masculin' : 'Féminin'}
              </span>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center">
              <User className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">Parent/Tuteur:</span>
              <span className="ml-2 font-medium">{enrollmentData.student?.nom_parent}</span>
            </div>
            <div className="flex items-center">
              <Phone className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">Contact:</span>
              <span className="ml-2 font-medium">{enrollmentData.student?.contact_parent}</span>
            </div>
            {enrollmentData.student?.adresse && (
              <div className="flex items-start">
                <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                <span className="text-sm text-gray-600">Adresse:</span>
                <span className="ml-2 font-medium">{enrollmentData.student.adresse}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Academic Assignment Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <GraduationCap className="h-5 w-5 mr-2 text-green-600" />
          Affectation Académique
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <span className="text-sm text-gray-600">Classe:</span>
            <p className="font-medium">{getSelectedClass()?.nom}</p>
          </div>
          <div>
            <span className="text-sm text-gray-600">Niveau:</span>
            <p className="font-medium">{getSelectedClass()?.niveau}</p>
          </div>
          <div>
            <span className="text-sm text-gray-600">Année scolaire:</span>
            <p className="font-medium">{enrollmentData.prerequisites?.activeSchoolYear?.annee || 'Non définie'}</p>
          </div>
        </div>

        {enrollmentData.academic?.assignedCourses && (
          <div className="mt-4">
            <span className="text-sm text-gray-600">Cours assignés:</span>
            <p className="font-medium">{enrollmentData.academic.assignedCourses.length} cours</p>
          </div>
        )}
      </div>

      {/* Financial Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <DollarSign className="h-5 w-5 mr-2 text-purple-600" />
          Résumé Financier
        </h3>

        <div className="space-y-4">
          {/* Initial Payments */}
          <div>
            <h4 className="font-medium text-gray-800 mb-2">Paiements Initiaux</h4>
            <div className="space-y-1 text-sm">
              {enrollmentData.financial?.registrationPaid && (
                <div className="flex justify-between">
                  <span>Frais d'inscription (payé):</span>
                  <span className="font-medium">{enrollmentData.financial.registrationFee?.toFixed(2)} Ar</span>
                </div>
              )}
              {enrollmentData.financial?.additionalFees?.map((fee: any, index: number) => (
                <div key={index} className="flex justify-between">
                  <span>{fee.description} (en attente):</span>
                  <span className="font-medium">{fee.amount?.toFixed(2)} Ar</span>
                </div>
              ))}
              <div className="border-t pt-1 flex justify-between font-medium">
                <span>Total Initial:</span>
                <span>{getTotalInitialAmount().toFixed(2)} Ar</span>
              </div>
            </div>
          </div>

          {/* Monthly Fees */}
          {enrollmentData.financial?.monthlyFeeSetup && (
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Frais Récurrents</h4>
              <div className="text-sm">
                <div className="flex justify-between">
                  <span>Frais mensuels:</span>
                  <span className="font-medium">{enrollmentData.financial.monthlyFee?.toFixed(2)} Ar / mois</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {submissionError && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
            <div>
              <h4 className="font-medium text-red-900">Erreur lors de l'inscription</h4>
              <p className="text-sm text-red-700">{submissionError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Final Actions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-4">Actions Finales</h3>

        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <p className="font-medium text-blue-900">Création du dossier élève</p>
              <p className="text-sm text-blue-700">
                Le dossier de l'élève sera créé dans le système avec toutes les informations saisies.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <p className="font-medium text-blue-900">Configuration des paiements</p>
              <p className="text-sm text-blue-700">
                Les frais et paiements seront enregistrés selon la configuration financière.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <p className="font-medium text-blue-900">Affectation aux cours</p>
              <p className="text-sm text-blue-700">
                L'élève sera automatiquement assigné aux cours de sa classe.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6 pt-4 border-t border-blue-200">
          <button
            onClick={handleFinalSubmission}
            disabled={isSubmitting}
            className={`w-full flex items-center justify-center px-6 py-3 text-white font-medium rounded-lg transition-colors ${isSubmitting
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700'
              }`}
          >
            {isSubmitting ? (
              <>
                <Loader className="h-5 w-5 mr-2 animate-spin" />
                Inscription en cours...
              </>
            ) : (
              <>
                <Send className="h-5 w-5 mr-2" />
                Finaliser l'Inscription
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
