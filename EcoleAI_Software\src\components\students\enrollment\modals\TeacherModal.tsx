import React, { useState } from 'react';
import { X, User, AlertCircle } from 'lucide-react';
import { apiClient } from '../../../../config/api';

interface TeacherModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (teacher: any) => void;
}

interface FormData {
  nom: string;
  prenom: string;
  specialite: string;
  email: string;
  telephone: string;
  date_embauche: string;
  is_active: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export default function TeacherModal({ isOpen, onClose, onSuccess }: TeacherModalProps) {
  const [formData, setFormData] = useState<FormData>({
    nom: '',
    prenom: '',
    specialite: '',
    email: '',
    telephone: '',
    date_embauche: '',
    is_active: true
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Le nom est requis';
    }

    if (!formData.prenom.trim()) {
      errors.prenom = 'Le prénom est requis';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Format d\'email invalide';
    }

    if (formData.telephone && formData.telephone.length < 8) {
      errors.telephone = 'Le numéro de téléphone doit contenir au moins 8 caractères';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Prepare data for submission
      const submitData = {
        ...formData,
        email: formData.email || null,
        telephone: formData.telephone || null,
        specialite: formData.specialite || null,
        date_embauche: formData.date_embauche || null
      };

      const response = await apiClient.post('/api/teachers/', submitData);
      onSuccess(response.data);
      onClose();
      resetForm();
    } catch (error: any) {
      console.error('Error creating teacher:', error);
      
      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        const newFormErrors: FormErrors = {};
        
        if (backendErrors.nom) {
          newFormErrors.nom = Array.isArray(backendErrors.nom) ? backendErrors.nom[0] : backendErrors.nom;
        }
        if (backendErrors.prenom) {
          newFormErrors.prenom = Array.isArray(backendErrors.prenom) ? backendErrors.prenom[0] : backendErrors.prenom;
        }
        if (backendErrors.email) {
          newFormErrors.email = Array.isArray(backendErrors.email) ? backendErrors.email[0] : backendErrors.email;
        }
        if (backendErrors.telephone) {
          newFormErrors.telephone = Array.isArray(backendErrors.telephone) ? backendErrors.telephone[0] : backendErrors.telephone;
        }
        
        setFormErrors(newFormErrors);
      } else {
        setFormErrors({ general: 'Erreur lors de la création du professeur' });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      nom: '',
      prenom: '',
      specialite: '',
      email: '',
      telephone: '',
      date_embauche: '',
      is_active: true
    });
    setFormErrors({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-1">
              Nouveau professeur
            </h2>
            <p className="text-sm text-gray-600">
              Créez un nouveau professeur
            </p>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
            type="button"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {formErrors.general && (
            <div className="flex items-center p-3 text-red-700 bg-red-100 border border-red-300 rounded-md">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span className="text-sm">{formErrors.general}</span>
            </div>
          )}

          {/* Nom */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom *
            </label>
            <input
              type="text"
              value={formData.nom}
              onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.nom 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="Nom de famille"
            />
            {formErrors.nom && (
              <p className="mt-1 text-sm text-red-600">{formErrors.nom}</p>
            )}
          </div>

          {/* Prénom */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prénom *
            </label>
            <input
              type="text"
              value={formData.prenom}
              onChange={(e) => setFormData({ ...formData, prenom: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.prenom 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="Prénom"
            />
            {formErrors.prenom && (
              <p className="mt-1 text-sm text-red-600">{formErrors.prenom}</p>
            )}
          </div>

          {/* Spécialité */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spécialité
            </label>
            <input
              type="text"
              value={formData.specialite}
              onChange={(e) => setFormData({ ...formData, specialite: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-200"
              placeholder="Ex: Mathématiques"
            />
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.email 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="<EMAIL>"
            />
            {formErrors.email && (
              <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
            )}
          </div>

          {/* Téléphone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Téléphone
            </label>
            <input
              type="tel"
              value={formData.telephone}
              onChange={(e) => setFormData({ ...formData, telephone: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.telephone 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="Numéro de téléphone"
            />
            {formErrors.telephone && (
              <p className="mt-1 text-sm text-red-600">{formErrors.telephone}</p>
            )}
          </div>

          {/* Date d'embauche */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date d'embauche
            </label>
            <input
              type="date"
              value={formData.date_embauche}
              onChange={(e) => setFormData({ ...formData, date_embauche: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-200"
            />
          </div>

          {/* Active */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Professeur actif</span>
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200"
              disabled={isLoading}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 hover:opacity-90 disabled:opacity-50"
              style={{ backgroundColor: '#0a1186' }}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </div>
              ) : (
                'Créer'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
