import { Alert<PERSON>riangle, X } from 'lucide-react';
import { useSessionWarning } from '../hooks/useSessionWarning';
import { useAuth } from '../contexts/AuthContext';
import { AuthService } from '../services/authService';
import { SecureTokenStorage } from '../utils/tokenStorage';

export default function SessionWarning() {
    const { logout } = useAuth();
    const { showWarning, timeLeft, dismissWarning, formatTimeLeft } = useSessionWarning({
        warningTime: 5 * 60 * 1000, // 5 minutes
        onExpired: () => {
            console.log('Session expired');
        }
    });

    if (!showWarning || !timeLeft) {
        return null;
    }

    const handleExtendSession = async () => {
        try {
            // Try to refresh the token to extend session
            const refreshToken = SecureTokenStorage.getRefreshToken();
            if (refreshToken) {
                const authResponse = await AuthService.refreshToken(refreshToken);
                SecureTokenStorage.setTokens(authResponse.access_token, authResponse.refresh_token);
                console.log('Session extended successfully');

                // Force dismiss warning immediately after successful refresh
                dismissWarning();

                // Also trigger a re-authentication check to update session state
                try {
                    const userData = await AuthService.getCurrentUser();
                    SecureTokenStorage.setUserData(userData);
                } catch (userError) {
                    console.error('Failed to refresh user data:', userError);
                }
            }
        } catch (error) {
            console.error('Failed to extend session:', error);
            // If refresh fails, logout
            logout();
        }
    };

    const handleLogout = () => {
        logout();
    };

    return (
        <div className="fixed top-4 right-4 z-50 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4 max-w-sm">
            <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" />
                <div className="flex-1">
                    <h3 className="text-sm font-medium text-yellow-800">
                        Session Expiring Soon
                    </h3>
                    <p className="mt-1 text-sm text-yellow-700">
                        Your session will expire in {formatTimeLeft(timeLeft)}
                    </p>
                    <div className="mt-3 flex space-x-2">
                        <button
                            onClick={handleExtendSession}
                            className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded text-sm font-medium"
                        >
                            Stay Logged In
                        </button>
                        <button
                            onClick={handleLogout}
                            className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded text-sm font-medium"
                        >
                            Logout
                        </button>
                    </div>
                </div>
                <button
                    onClick={dismissWarning}
                    className="ml-2 text-yellow-400 hover:text-yellow-600"
                >
                    <X className="h-4 w-4" />
                </button>
            </div>
        </div>
    );
}