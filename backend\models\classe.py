# models/classe.py
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from db import Base

class Classe(Base):
    __tablename__ = "classes"

    id = Column(Integer, primary_key=True, index=True)
    nom = Column(String, nullable=False, index=True)  # "6ème A", "Terminale S2"
    niveau = Column(String, nullable=False, index=True)  # CP, CM2, Lycée, etc.
    annee_scolaire_id = Column(Integer, ForeignKey("school_years.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    school_year = relationship("SchoolYear", back_populates="classes")
    students = relationship("Student", back_populates="classe")
    courses = relationship("Course", back_populates="classe")
    timetables = relationship("Timetable", back_populates="classe")

    # Calculated property for student count
    @property
    def effectif(self):
        return len(self.students) if self.students else 0
