#!/usr/bin/env python3
"""
Comprehensive test suite for École AI School Management System
Tests all new endpoints and functionality
"""

import requests
import json
from datetime import datetime, date
from typing import Dict, Any, Optional

class SchoolManagementTester:
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.access_token: Optional[str] = None
        
    def log(self, message: str, level: str = "INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """Login and get access token"""
        try:
            response = self.session.post(
                f"{self.base_url}/auth/token",
                data={"username": username, "password": password},
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get("access_token")
                self.session.headers.update({"Authorization": f"Bearer {self.access_token}"})
                self.log("✅ Login successful")
                return True
            else:
                self.log(f"❌ Login failed: {response.status_code} - {response.text}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ Login error: {e}", "ERROR")
            return False
    
    def test_school_years(self) -> bool:
        """Test school year endpoints"""
        self.log("Testing School Years endpoints...")
        
        try:
            # Get all school years
            response = self.session.get(f"{self.base_url}/api/school-years/")
            if response.status_code != 200:
                self.log(f"❌ Failed to get school years: {response.status_code}", "ERROR")
                return False
            
            school_years = response.json()
            self.log(f"✅ Retrieved {len(school_years)} school years")
            
            # Get active school year
            response = self.session.get(f"{self.base_url}/api/school-years/active")
            if response.status_code == 200:
                active_year = response.json()
                self.log(f"✅ Active school year: {active_year['annee']}")
            else:
                self.log("⚠️ No active school year found")
            
            return True
        except Exception as e:
            self.log(f"❌ School years test error: {e}", "ERROR")
            return False
    
    def test_subjects(self) -> bool:
        """Test subjects endpoints"""
        self.log("Testing Subjects endpoints...")
        
        try:
            # Get all subjects
            response = self.session.get(f"{self.base_url}/api/subjects/")
            if response.status_code != 200:
                self.log(f"❌ Failed to get subjects: {response.status_code}", "ERROR")
                return False
            
            subjects = response.json()
            self.log(f"✅ Retrieved {len(subjects)} subjects")
            
            # Test subject performance if subjects exist
            if subjects:
                subject_id = subjects[0]["id"]
                response = self.session.get(f"{self.base_url}/api/subjects/{subject_id}/performance")
                if response.status_code == 200:
                    performance = response.json()
                    self.log(f"✅ Subject performance data retrieved for {subjects[0]['nom']}")
                else:
                    self.log("⚠️ No performance data available yet")
            
            return True
        except Exception as e:
            self.log(f"❌ Subjects test error: {e}", "ERROR")
            return False
    
    def test_teachers(self) -> bool:
        """Test teachers endpoints"""
        self.log("Testing Teachers endpoints...")
        
        try:
            # Get all teachers
            response = self.session.get(f"{self.base_url}/api/teachers/")
            if response.status_code != 200:
                self.log(f"❌ Failed to get teachers: {response.status_code}", "ERROR")
                return False
            
            teachers = response.json()
            self.log(f"✅ Retrieved {len(teachers)} teachers")
            
            # Test search functionality
            response = self.session.get(f"{self.base_url}/api/teachers/?search=RAKOTO")
            if response.status_code == 200:
                search_results = response.json()
                self.log(f"✅ Teacher search returned {len(search_results)} results")
            
            return True
        except Exception as e:
            self.log(f"❌ Teachers test error: {e}", "ERROR")
            return False
    
    def test_classes(self) -> bool:
        """Test classes endpoints"""
        self.log("Testing Classes endpoints...")
        
        try:
            # Get all classes
            response = self.session.get(f"{self.base_url}/api/classes/")
            if response.status_code != 200:
                self.log(f"❌ Failed to get classes: {response.status_code}", "ERROR")
                return False
            
            classes = response.json()
            self.log(f"✅ Retrieved {len(classes)} classes")
            
            # Test class ranking if classes exist
            if classes:
                class_id = classes[0]["id"]
                response = self.session.get(f"{self.base_url}/api/classes/{class_id}/ranking")
                if response.status_code == 200:
                    ranking = response.json()
                    self.log(f"✅ Class ranking retrieved for {classes[0]['nom']}")
                else:
                    self.log("⚠️ No ranking data available yet")
            
            return True
        except Exception as e:
            self.log(f"❌ Classes test error: {e}", "ERROR")
            return False
    
    def test_students(self) -> bool:
        """Test students endpoints"""
        self.log("Testing Students endpoints...")
        
        try:
            # Get all students
            response = self.session.get(f"{self.base_url}/api/students/")
            if response.status_code != 200:
                self.log(f"❌ Failed to get students: {response.status_code}", "ERROR")
                return False
            
            students = response.json()
            self.log(f"✅ Retrieved {len(students)} students")
            
            # Test matricule generation
            response = self.session.get(f"{self.base_url}/api/students/generate-matricule")
            if response.status_code == 200:
                matricule_data = response.json()
                self.log(f"✅ Generated matricule: {matricule_data['matricule']}")
            
            # Test student academic summary if students exist
            if students:
                student_id = students[0]["id"]
                response = self.session.get(f"{self.base_url}/api/students/{student_id}/academic-summary")
                if response.status_code == 200:
                    summary = response.json()
                    self.log(f"✅ Academic summary retrieved for {students[0]['prenom']} {students[0]['nom']}")
                
                # Test financial status
                response = self.session.get(f"{self.base_url}/api/students/{student_id}/financial-status")
                if response.status_code == 200:
                    financial = response.json()
                    self.log(f"✅ Financial status retrieved for {students[0]['prenom']} {students[0]['nom']}")
            
            return True
        except Exception as e:
            self.log(f"❌ Students test error: {e}", "ERROR")
            return False
    
    def test_payments(self) -> bool:
        """Test payments endpoints"""
        self.log("Testing Payments endpoints...")
        
        try:
            # Get all payments
            response = self.session.get(f"{self.base_url}/api/payments/")
            if response.status_code != 200:
                self.log(f"❌ Failed to get payments: {response.status_code}", "ERROR")
                return False
            
            payments = response.json()
            self.log(f"✅ Retrieved {len(payments)} payments")
            
            # Test financial dashboard
            response = self.session.get(f"{self.base_url}/api/payments/dashboard")
            if response.status_code == 200:
                dashboard = response.json()
                self.log(f"✅ Financial dashboard retrieved")
                self.log(f"   - Total paid: {dashboard.get('totals', {}).get('paid', 0)}")
                self.log(f"   - Total pending: {dashboard.get('totals', {}).get('pending', 0)}")
            
            # Test pending payments
            response = self.session.get(f"{self.base_url}/api/payments/pending")
            if response.status_code == 200:
                pending = response.json()
                self.log(f"✅ Retrieved {len(pending)} pending payments")
            
            return True
        except Exception as e:
            self.log(f"❌ Payments test error: {e}", "ERROR")
            return False
    
    def test_api_documentation(self) -> bool:
        """Test API documentation accessibility"""
        self.log("Testing API documentation...")
        
        try:
            response = self.session.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                self.log("✅ API documentation accessible at /docs")
                return True
            else:
                self.log(f"❌ API documentation not accessible: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ API documentation test error: {e}", "ERROR")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all school management system tests"""
        self.log("=" * 60)
        self.log("Starting École AI School Management System Tests")
        self.log("=" * 60)
        
        results = {}
        
        # Login first
        if not self.login():
            self.log("❌ Cannot proceed without authentication", "ERROR")
            return {"authentication": False}
        
        # Run all tests
        results["authentication"] = True
        results["api_documentation"] = self.test_api_documentation()
        results["school_years"] = self.test_school_years()
        results["subjects"] = self.test_subjects()
        results["teachers"] = self.test_teachers()
        results["classes"] = self.test_classes()
        results["students"] = self.test_students()
        results["payments"] = self.test_payments()
        
        # Summary
        self.log("=" * 60)
        self.log("Test Results Summary:")
        self.log("=" * 60)
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.log(f"{test_name.replace('_', ' ').title()}: {status}")
        
        self.log("=" * 60)
        self.log(f"Overall: {passed}/{total} tests passed")
        
        if passed == total:
            self.log("🎉 All tests passed! School Management System is working correctly.")
        else:
            self.log("⚠️  Some tests failed. Check the logs above for details.")
        
        return results

if __name__ == "__main__":
    tester = SchoolManagementTester()
    results = tester.run_all_tests()
