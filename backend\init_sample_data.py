#!/usr/bin/env python3
"""
Initialize the database with sample data for École AI
"""

from datetime import date, datetime, timedelta
from db import SessionLocal, engine, Base
from models.user import User
from models.school_year import SchoolYear
from models.subject import Subject
from models.teacher import Teacher
from models.classe import Classe
from models.student import Student
from models.course import Course
from models.grade import Grade
from models.attendance import Attendance
from models.payment import Payment
from models.timetable import Timetable, DayOfWeek
from utils.security import hash_password

def create_sample_data():
    """Create comprehensive sample data for testing"""
    db = SessionLocal()
    
    try:
        print("Creating sample data...")
        
        # 1. Create admin user
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hash_password("admin123"),
            full_name="Administrateur École AI",
            phone="+261 32 12 345 67",
            role="admin",
            disabled=False
        )
        db.add(admin_user)
        
        # 2. Create school year
        current_year = SchoolYear(
            annee="2024-2025",
            date_debut=date(2024, 9, 1),
            date_fin=date(2025, 6, 30),
            is_active=True
        )
        db.add(current_year)
        db.commit()
        db.refresh(current_year)
        
        # 3. Create subjects
        subjects_data = [
            {"nom": "Mathématiques", "coef": 3.0, "description": "Mathematics and numerical reasoning"},
            {"nom": "Français", "coef": 3.0, "description": "French language and literature"},
            {"nom": "Anglais", "coef": 2.0, "description": "English language"},
            {"nom": "Sciences Physiques", "coef": 2.5, "description": "Physics and Chemistry"},
            {"nom": "Sciences Naturelles", "coef": 2.0, "description": "Biology and Natural Sciences"},
            {"nom": "Histoire-Géographie", "coef": 2.0, "description": "History and Geography"},
            {"nom": "Éducation Physique", "coef": 1.0, "description": "Physical Education"},
            {"nom": "Arts Plastiques", "coef": 1.0, "description": "Visual Arts"}
        ]
        
        subjects = []
        for subject_data in subjects_data:
            subject = Subject(**subject_data)
            db.add(subject)
            subjects.append(subject)
        
        db.commit()
        
        # 4. Create teachers
        teachers_data = [
            {"nom": "RAKOTO", "prenom": "Jean", "specialite": "Mathématiques", "email": "<EMAIL>", "telephone": "+261 32 11 111 11"},
            {"nom": "RABE", "prenom": "Marie", "specialite": "Français", "email": "<EMAIL>", "telephone": "+261 32 22 222 22"},
            {"nom": "ANDRY", "prenom": "Paul", "specialite": "Anglais", "email": "<EMAIL>", "telephone": "+261 32 33 333 33"},
            {"nom": "HERY", "prenom": "Sophie", "specialite": "Sciences Physiques", "email": "<EMAIL>", "telephone": "+261 32 44 444 44"},
            {"nom": "RAVO", "prenom": "Michel", "specialite": "Sciences Naturelles", "email": "<EMAIL>", "telephone": "+261 32 55 555 55"},
            {"nom": "FARA", "prenom": "Claudine", "specialite": "Histoire-Géographie", "email": "<EMAIL>", "telephone": "+261 32 66 666 66"}
        ]
        
        teachers = []
        for teacher_data in teachers_data:
            teacher_data["date_embauche"] = date(2023, 9, 1)
            teacher = Teacher(**teacher_data)
            db.add(teacher)
            teachers.append(teacher)
        
        db.commit()
        
        # 5. Create classes
        classes_data = [
            {"nom": "6ème A", "niveau": "6ème"},
            {"nom": "6ème B", "niveau": "6ème"},
            {"nom": "5ème A", "niveau": "5ème"},
            {"nom": "4ème A", "niveau": "4ème"},
            {"nom": "3ème A", "niveau": "3ème"},
            {"nom": "Seconde A", "niveau": "Seconde"},
            {"nom": "Première S", "niveau": "Première"},
            {"nom": "Terminale S", "niveau": "Terminale"}
        ]
        
        classes = []
        for class_data in classes_data:
            class_data["annee_scolaire_id"] = current_year.id
            classe = Classe(**class_data)
            db.add(classe)
            classes.append(classe)
        
        db.commit()
        
        # 6. Create students
        students_data = [
            {"matricule": "EAI2024001", "nom": "RASOLONDRAIBE", "prenom": "Solo", "date_naissance": date(2010, 5, 15), "sexe": "M", "adresse": "Antananarivo", "nom_parent": "RASOLONDRAIBE Paul", "contact_parent": "+261 32 43 789 12", "classe_id": classes[0].id},
            {"matricule": "EAI2024002", "nom": "RAVELOJAONA", "prenom": "Marie", "date_naissance": date(2011, 3, 22), "sexe": "F", "adresse": "Antananarivo", "nom_parent": "RAVELOJAONA Vonjy", "contact_parent": "+261 33 33 899 13", "classe_id": classes[0].id},
            {"matricule": "EAI2024003", "nom": "RANDRIAMAMPIONONA", "prenom": "Hery", "date_naissance": date(2010, 8, 10), "sexe": "M", "adresse": "Antananarivo", "nom_parent": "RANDRIAMAMPIONONA Lala", "contact_parent": "+261 34 12 456 78", "classe_id": classes[1].id},
            {"matricule": "EAI2024004", "nom": "RAZAFY", "prenom": "Naina", "date_naissance": date(2009, 12, 5), "sexe": "F", "adresse": "Antananarivo", "nom_parent": "RAZAFY Miora", "contact_parent": "+261 32 98 765 43", "classe_id": classes[2].id},
            {"matricule": "EAI2024005", "nom": "ANDRIANTSOA", "prenom": "Koto", "date_naissance": date(2008, 7, 18), "sexe": "M", "adresse": "Antananarivo", "nom_parent": "ANDRIANTSOA Fidy", "contact_parent": "+261 33 87 654 32", "classe_id": classes[3].id}
        ]
        
        students = []
        for student_data in students_data:
            student = Student(**student_data)
            db.add(student)
            students.append(student)
        
        db.commit()
        
        # 7. Create courses (teacher-subject-class assignments)
        courses = []
        for i, classe in enumerate(classes[:3]):  # First 3 classes
            for j, subject in enumerate(subjects[:4]):  # First 4 subjects
                teacher_index = j % len(teachers)
                course = Course(
                    enseignant_id=teachers[teacher_index].id,
                    matiere_id=subject.id,
                    classe_id=classe.id,
                    horaire=f"Lundi 08:00-09:00, Mercredi 10:00-11:00"
                )
                db.add(course)
                courses.append(course)
        
        db.commit()
        
        # 8. Create sample grades
        import random
        for student in students[:3]:  # First 3 students
            for course in courses:
                if course.classe_id == student.classe_id:
                    # Create 3-5 grades per student per course
                    for _ in range(random.randint(3, 5)):
                        grade_types = ["DS", "Exam", "Test", "Homework"]
                        grade = Grade(
                            eleve_id=student.id,
                            cours_id=course.id,
                            note=round(random.uniform(8, 18), 1),
                            type=random.choice(grade_types),
                            date=date.today() - timedelta(days=random.randint(1, 60))
                        )
                        db.add(grade)
        
        # 9. Create sample attendance
        for student in students[:3]:
            for course in courses:
                if course.classe_id == student.classe_id:
                    # Create attendance for last 30 days
                    for i in range(30):
                        attendance_date = date.today() - timedelta(days=i)
                        if attendance_date.weekday() < 5:  # Weekdays only
                            statuses = ["present", "present", "present", "absent", "late"]  # Weighted towards present
                            attendance = Attendance(
                                eleve_id=student.id,
                                cours_id=course.id,
                                date=attendance_date,
                                statut=random.choice(statuses)
                            )
                            db.add(attendance)
        
        # 10. Create sample payments
        payment_reasons = ["registration", "monthly_fee", "cafeteria", "books"]
        for student in students:
            # Registration fee
            payment = Payment(
                eleve_id=student.id,
                montant=50000.0,  # 50,000 Ariary
                motif="registration",
                date_paiement=date(2024, 8, 15),
                statut="paid",
                reference=f"PAY202408{student.id:03d}001"
            )
            db.add(payment)
            
            # Monthly fees for September and October
            for month in ["September", "October"]:
                payment = Payment(
                    eleve_id=student.id,
                    montant=30000.0,  # 30,000 Ariary per month
                    motif="monthly_fee",
                    date_paiement=date(2024, 9 if month == "September" else 10, 5),
                    statut="paid" if month == "September" else "pending",
                    reference=f"PAY2024{9 if month == 'September' else 10}{student.id:03d}001",
                    commentaire=f"Monthly fee for {month}"
                )
                db.add(payment)

        # 11. Create sample timetables
        from datetime import time

        timetables = []
        time_slots = [
            (time(8, 0), time(9, 0)),   # 8:00-9:00
            (time(9, 0), time(10, 0)),  # 9:00-10:00
            (time(10, 30), time(11, 30)), # 10:30-11:30 (after break)
            (time(11, 30), time(12, 30)), # 11:30-12:30
            (time(14, 0), time(15, 0)),   # 14:00-15:00 (after lunch)
            (time(15, 0), time(16, 0)),   # 15:00-16:00
        ]

        days = [DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY]
        rooms = ["Salle A1", "Salle A2", "Salle B1", "Salle B2", "Laboratoire", "Bibliothèque"]

        for classe in classes[:3]:  # First 3 classes
            for day_idx, day in enumerate(days):
                # Create 4-5 time slots per day for each class
                daily_slots = time_slots[:5] if day_idx < 3 else time_slots[:4]

                for slot_idx, (start_time, end_time) in enumerate(daily_slots):
                    # Assign subjects cyclically
                    subject = subjects[slot_idx % len(subjects)]

                    # Find a teacher for this subject (prefer matching specialty)
                    teacher = None
                    for t in teachers:
                        if t.specialite and subject.nom.lower() in t.specialite.lower():
                            teacher = t
                            break
                    if not teacher:
                        teacher = teachers[slot_idx % len(teachers)]

                    # Assign room
                    room = rooms[slot_idx % len(rooms)]

                    timetable_entry = Timetable(
                        classe_id=classe.id,
                        matiere_id=subject.id,
                        enseignant_id=teacher.id,
                        annee_scolaire_id=classe.annee_scolaire_id,
                        jour_semaine=day,
                        heure_debut=start_time,
                        heure_fin=end_time,
                        salle=room,
                        description=f"{subject.nom} - {classe.nom}",
                        is_active=True
                    )
                    db.add(timetable_entry)
                    timetables.append(timetable_entry)

        db.commit()
        print("✅ Sample data created successfully!")
        
        # Print summary
        print("\n📊 Summary of created data:")
        print(f"- 1 Admin user (username: admin, password: admin123)")
        print(f"- 1 School year (2024-2025)")
        print(f"- {len(subjects)} Subjects")
        print(f"- {len(teachers)} Teachers")
        print(f"- {len(classes)} Classes")
        print(f"- {len(students)} Students")
        print(f"- {len(courses)} Courses")
        print(f"- {len(timetables)} Timetable entries")
        print(f"- Sample grades, attendance, and payments")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

def init_database():
    """Initialize database tables and create sample data"""
    print("🔧 Initializing database...")
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created!")
    
    # Create sample data
    create_sample_data()

if __name__ == "__main__":
    init_database()
