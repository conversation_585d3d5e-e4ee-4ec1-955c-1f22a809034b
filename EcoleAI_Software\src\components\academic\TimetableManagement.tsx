import React, { useState, useEffect } from 'react';
import { Calendar, Plus, Search, Filter, Edit, <PERSON>, Clock, <PERSON>P<PERSON>, Loader, Trash2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { apiClient } from '../../config/api';

interface Timetable {
  id: number;
  classe_id: number;
  matiere_id: number;
  enseignant_id: number;
  annee_scolaire_id: number;
  jour_semaine: string;
  heure_debut: string;
  heure_fin: string;
  salle?: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  classe?: {
    id: number;
    nom: string;
    niveau: string;
  };
  subject?: {
    id: number;
    nom: string;
    coef: number;
  };
  teacher?: {
    id: number;
    nom: string;
    prenom: string;
    specialite?: string;
  };
  school_year?: {
    id: number;
    annee: string;
    is_active: boolean;
  };
}

interface TimetableFormData {
  classe_id: number;
  matiere_id: number;
  enseignant_id: number;
  annee_scolaire_id: number;
  jour_semaine: string;
  heure_debut: string;
  heure_fin: string;
  salle: string;
  description: string;
  is_active: boolean;
}

interface FormErrors {
  [key: string]: string;
}

interface Class {
  id: number;
  nom: string;
  niveau: string;
  annee_scolaire_id: number;
}

interface Subject {
  id: number;
  nom: string;
  coef: number;
}

interface Teacher {
  id: number;
  nom: string;
  prenom: string;
  specialite?: string;
  is_active: boolean;
}

interface SchoolYear {
  id: number;
  annee: string;
  is_active: boolean;
}

const DAYS_OF_WEEK = [
  { value: 'monday', label: 'Lundi' },
  { value: 'tuesday', label: 'Mardi' },
  { value: 'wednesday', label: 'Mercredi' },
  { value: 'thursday', label: 'Jeudi' },
  { value: 'friday', label: 'Vendredi' },
  { value: 'saturday', label: 'Samedi' },
  { value: 'sunday', label: 'Dimanche' }
];

export default function TimetableManagement() {
  const { user } = useAuth();
  const [timetables, setTimetables] = useState<Timetable[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [schoolYears, setSchoolYears] = useState<SchoolYear[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [classFilter, setClassFilter] = useState<string>('');
  const [teacherFilter, setTeacherFilter] = useState<string>('');
  const [dayFilter, setDayFilter] = useState<string>('');
  const [showModal, setShowModal] = useState(false);
  const [editingTimetable, setEditingTimetable] = useState<Timetable | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<TimetableFormData>({
    classe_id: 0,
    matiere_id: 0,
    enseignant_id: 0,
    annee_scolaire_id: 0,
    jour_semaine: 'monday',
    heure_debut: '08:00',
    heure_fin: '09:00',
    salle: '',
    description: '',
    is_active: true
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [timetablesRes, classesRes, subjectsRes, teachersRes, schoolYearsRes] = await Promise.all([
        apiClient.get('/api/timetables/'),
        apiClient.get('/api/classes/'),
        apiClient.get('/api/subjects/'),
        apiClient.get('/api/teachers/'),
        apiClient.get('/api/school-years/')
      ]);

      setTimetables(timetablesRes.data);
      setClasses(classesRes.data);
      setSubjects(subjectsRes.data);
      setTeachers(teachersRes.data);
      setSchoolYears(schoolYearsRes.data);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const filteredTimetables = timetables.filter(timetable => {
    const matchesSearch = searchTerm === '' ||
      (timetable.classe?.nom && timetable.classe.nom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (timetable.subject?.nom && timetable.subject.nom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (timetable.teacher?.nom && timetable.teacher.nom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (timetable.teacher?.prenom && timetable.teacher.prenom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (timetable.salle && timetable.salle.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesClass = classFilter === '' || timetable.classe_id.toString() === classFilter;
    const matchesTeacher = teacherFilter === '' || timetable.enseignant_id.toString() === teacherFilter;
    const matchesDay = dayFilter === '' || timetable.jour_semaine === dayFilter;

    return matchesSearch && matchesClass && matchesTeacher && matchesDay;
  });

  const canEdit = user?.role === 'admin' || user?.role === 'secretary';

  const resetForm = () => {
    const activeSchoolYear = schoolYears.find(sy => sy.is_active);
    setFormData({
      classe_id: 0,
      matiere_id: 0,
      enseignant_id: 0,
      annee_scolaire_id: activeSchoolYear?.id || 0,
      jour_semaine: 'monday',
      heure_debut: '08:00',
      heure_fin: '09:00',
      salle: '',
      description: '',
      is_active: true
    });
    setFormErrors({});
  };

  const handleAddNew = () => {
    if (!canEdit) return;
    setEditingTimetable(null);
    resetForm();
    setShowModal(true);
  };

  const handleEdit = (timetable: Timetable) => {
    if (!canEdit) return;
    setEditingTimetable(timetable);
    setFormData({
      classe_id: timetable.classe_id,
      matiere_id: timetable.matiere_id,
      enseignant_id: timetable.enseignant_id,
      annee_scolaire_id: timetable.annee_scolaire_id,
      jour_semaine: timetable.jour_semaine,
      heure_debut: timetable.heure_debut,
      heure_fin: timetable.heure_fin,
      salle: timetable.salle || '',
      description: timetable.description || '',
      is_active: timetable.is_active
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleView = (timetable: Timetable) => {
    // Could navigate to a detailed view or show a read-only modal
    console.log('View timetable:', timetable);
  };

  const formatTime = (timeString: string) => {
    return timeString.slice(0, 5); // Remove seconds if present
  };

  const getDayLabel = (day: string) => {
    const dayObj = DAYS_OF_WEEK.find(d => d.value === day);
    return dayObj ? dayObj.label : day;
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.classe_id) errors.classe_id = 'La classe est requise';
    if (!formData.matiere_id) errors.matiere_id = 'La matière est requise';
    if (!formData.enseignant_id) errors.enseignant_id = 'L\'enseignant est requis';
    if (!formData.annee_scolaire_id) errors.annee_scolaire_id = 'L\'année scolaire est requise';
    if (!formData.heure_debut) errors.heure_debut = 'L\'heure de début est requise';
    if (!formData.heure_fin) errors.heure_fin = 'L\'heure de fin est requise';

    if (formData.heure_debut && formData.heure_fin && formData.heure_debut >= formData.heure_fin) {
      errors.heure_fin = 'L\'heure de fin doit être après l\'heure de début';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setFormLoading(true);

      if (editingTimetable) {
        await apiClient.put(`/api/timetables/${editingTimetable.id}`, formData);
      } else {
        await apiClient.post('/api/timetables/', formData);
      }

      await fetchData();
      setShowModal(false);
      resetForm();
    } catch (err: any) {
      console.error('Error saving timetable:', err);
      if (err.response?.status === 409) {
        setError('Conflit d\'horaire détecté. Veuillez vérifier les créneaux existants.');
      } else {
        setError('Erreur lors de la sauvegarde');
      }
    } finally {
      setFormLoading(false);
    }
  };

  const handleDelete = async (timetable: Timetable) => {
    if (!canEdit || !confirm('Êtes-vous sûr de vouloir supprimer cette entrée d\'emploi du temps ?')) return;

    try {
      await apiClient.delete(`/api/timetables/${timetable.id}`);
      await fetchData();
    } catch (err) {
      console.error('Error deleting timetable:', err);
      setError('Erreur lors de la suppression');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Emplois du Temps</h1>
          <p className="text-gray-600 mt-1">
            {canEdit ? 'Gestion des créneaux horaires' : 'Consultation des emplois du temps'}
          </p>
        </div>
        {canEdit && (
          <button
            onClick={handleAddNew}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
            style={{ backgroundColor: '#0a1186' }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Créneau
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Créneaux</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{timetables.length}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3f2fd' }}>
              <Calendar className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Classes Actives</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{new Set(timetables.map(t => t.classe_id)).size}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#f3e5f5' }}>
              <Filter className="h-6 w-6 text-purple-700" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Enseignants Assignés</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{new Set(timetables.map(t => t.enseignant_id)).size}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#fff3e0' }}>
              <Clock className="h-6 w-6 text-orange-700" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Salles Utilisées</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{new Set(timetables.map(t => t.salle).filter(Boolean)).size}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e8f5e8' }}>
              <MapPin className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={classFilter}
            onChange={(e) => setClassFilter(e.target.value)}
          >
            <option value="">Toutes les classes</option>
            {classes.map(classe => (
              <option key={classe.id} value={classe.id.toString()}>
                {classe.nom} ({classe.niveau})
              </option>
            ))}
          </select>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={teacherFilter}
            onChange={(e) => setTeacherFilter(e.target.value)}
          >
            <option value="">Tous les enseignants</option>
            {teachers.filter(t => t.is_active).map(teacher => (
              <option key={teacher.id} value={teacher.id.toString()}>
                {teacher.prenom} {teacher.nom}
              </option>
            ))}
          </select>
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={dayFilter}
            onChange={(e) => setDayFilter(e.target.value)}
          >
            <option value="">Tous les jours</option>
            {DAYS_OF_WEEK.map(day => (
              <option key={day.value} value={day.value}>
                {day.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Timetables Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead style={{ backgroundColor: '#0a1186' }}>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Jour & Horaire
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Classe
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Matière
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Enseignant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Salle
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTimetables.map((timetable) => (
                <tr key={timetable.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {getDayLabel(timetable.jour_semaine)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatTime(timetable.heure_debut)} - {formatTime(timetable.heure_fin)}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {timetable.classe?.nom}
                    </div>
                    <div className="text-sm text-gray-500">
                      {timetable.classe?.niveau}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {timetable.subject?.nom}
                    </div>
                    <div className="text-sm text-gray-500">
                      Coef: {timetable.subject?.coef}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {timetable.teacher?.prenom} {timetable.teacher?.nom}
                    </div>
                    <div className="text-sm text-gray-500">
                      {timetable.teacher?.specialite}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {timetable.salle || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleView(timetable)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Voir les détails"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {canEdit && (
                        <>
                          <button
                            onClick={() => handleEdit(timetable)}
                            className="text-yellow-600 hover:text-yellow-900 p-1 rounded"
                            title="Modifier"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(timetable)}
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Empty State */}
          {filteredTimetables.length === 0 && !loading && (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun créneau trouvé</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || classFilter || teacherFilter || dayFilter
                  ? 'Aucun créneau ne correspond aux critères de recherche.'
                  : 'Aucun créneau n\'est encore programmé dans le système.'
                }
              </p>
              {canEdit && (
                <button
                  onClick={handleAddNew}
                  className="inline-flex items-center px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter le premier créneau
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Modal pour ajouter/modifier un créneau */}
      {showModal && canEdit && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">
                {editingTimetable ? 'Modifier le créneau' : 'Nouveau créneau'}
              </h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Classe */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Classe *
                  </label>
                  <select
                    value={formData.classe_id}
                    onChange={(e) => setFormData({ ...formData, classe_id: parseInt(e.target.value) })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.classe_id ? 'border-red-500' : 'border-gray-300'
                      }`}
                    required
                  >
                    <option value={0}>Sélectionner une classe</option>
                    {classes.map(classe => (
                      <option key={classe.id} value={classe.id}>
                        {classe.nom} ({classe.niveau})
                      </option>
                    ))}
                  </select>
                  {formErrors.classe_id && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.classe_id}</p>
                  )}
                </div>

                {/* Matière */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Matière *
                  </label>
                  <select
                    value={formData.matiere_id}
                    onChange={(e) => setFormData({ ...formData, matiere_id: parseInt(e.target.value) })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.matiere_id ? 'border-red-500' : 'border-gray-300'
                      }`}
                    required
                  >
                    <option value={0}>Sélectionner une matière</option>
                    {subjects.map(subject => (
                      <option key={subject.id} value={subject.id}>
                        {subject.nom} (Coef: {subject.coef})
                      </option>
                    ))}
                  </select>
                  {formErrors.matiere_id && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.matiere_id}</p>
                  )}
                </div>

                {/* Enseignant */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Enseignant *
                  </label>
                  <select
                    value={formData.enseignant_id}
                    onChange={(e) => setFormData({ ...formData, enseignant_id: parseInt(e.target.value) })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.enseignant_id ? 'border-red-500' : 'border-gray-300'
                      }`}
                    required
                  >
                    <option value={0}>Sélectionner un enseignant</option>
                    {teachers.filter(t => t.is_active).map(teacher => (
                      <option key={teacher.id} value={teacher.id}>
                        {teacher.prenom} {teacher.nom} {teacher.specialite ? `(${teacher.specialite})` : ''}
                      </option>
                    ))}
                  </select>
                  {formErrors.enseignant_id && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.enseignant_id}</p>
                  )}
                </div>

                {/* Année scolaire */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Année scolaire *
                  </label>
                  <select
                    value={formData.annee_scolaire_id}
                    onChange={(e) => setFormData({ ...formData, annee_scolaire_id: parseInt(e.target.value) })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.annee_scolaire_id ? 'border-red-500' : 'border-gray-300'
                      }`}
                    required
                  >
                    <option value={0}>Sélectionner une année</option>
                    {schoolYears.map(year => (
                      <option key={year.id} value={year.id}>
                        {year.annee} {year.is_active ? '(Active)' : ''}
                      </option>
                    ))}
                  </select>
                  {formErrors.annee_scolaire_id && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.annee_scolaire_id}</p>
                  )}
                </div>

                {/* Jour de la semaine */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Jour de la semaine *
                  </label>
                  <select
                    value={formData.jour_semaine}
                    onChange={(e) => setFormData({ ...formData, jour_semaine: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    {DAYS_OF_WEEK.map(day => (
                      <option key={day.value} value={day.value}>
                        {day.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Salle */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Salle
                  </label>
                  <input
                    type="text"
                    value={formData.salle}
                    onChange={(e) => setFormData({ ...formData, salle: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Ex: Salle A1, Laboratoire..."
                  />
                </div>

                {/* Heure de début */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Heure de début *
                  </label>
                  <input
                    type="time"
                    value={formData.heure_debut}
                    onChange={(e) => setFormData({ ...formData, heure_debut: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.heure_debut ? 'border-red-500' : 'border-gray-300'
                      }`}
                    required
                  />
                  {formErrors.heure_debut && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.heure_debut}</p>
                  )}
                </div>

                {/* Heure de fin */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Heure de fin *
                  </label>
                  <input
                    type="time"
                    value={formData.heure_fin}
                    onChange={(e) => setFormData({ ...formData, heure_fin: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.heure_fin ? 'border-red-500' : 'border-gray-300'
                      }`}
                    required
                  />
                  {formErrors.heure_fin && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.heure_fin}</p>
                  )}
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Notes ou informations supplémentaires..."
                />
              </div>

              {/* Statut actif */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                  Créneau actif
                </label>
              </div>

              {/* Boutons */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  className="px-4 py-2 text-white rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  {formLoading ? (
                    <div className="flex items-center">
                      <Loader className="h-4 w-4 animate-spin mr-2" />
                      Enregistrement...
                    </div>
                  ) : (
                    editingTimetable ? 'Modifier' : 'Créer'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
