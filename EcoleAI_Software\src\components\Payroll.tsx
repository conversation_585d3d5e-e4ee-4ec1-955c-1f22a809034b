import React, { useState } from 'react';
import { Briefcase, Download, Calculator, DollarSign, FileText, Calendar, Plus, Eye } from 'lucide-react';

interface PayrollRecord {
  id: string;
  teacherId: string;
  teacherName: string;
  baseSalary: number;
  bonuses: number;
  deductions: number;
  advances: number;
  netSalary: number;
  month: string;
  year: number;
  status: 'draft' | 'paid';
}

const mockPayroll: PayrollRecord[] = [
  {
    id: '1',
    teacherId: '1',
    teacherName: 'RAVELOJAONA Paul',
    baseSalary: 180000,
    bonuses: 15000,
    deductions: 8000,
    advances: 0,
    netSalary: 187000,
    month: 'Janvier',
    year: 2024,
    status: 'paid'
  },
  {
    id: '2',
    teacherId: '2',
    teacherName: 'RAKOTO Marie',
    baseSalary: 175000,
    bonuses: 10000,
    deductions: 7500,
    advances: 25000,
    netSalary: 152500,
    month: 'Janvier',
    year: 2024,
    status: 'paid'
  },
  {
    id: '3',
    teacherId: '3',
    teacherName: 'DERA Toky',
    baseSalary: 165000,
    bonuses: 8000,
    deductions: 6000,
    advances: 0,
    netSalary: 167000,
    month: 'Janvier',
    year: 2024,
    status: 'draft'
  }
];

export default function Payroll() {
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>(mockPayroll);
  const [selectedMonth, setSelectedMonth] = useState('Janvier');
  const [selectedYear, setSelectedYear] = useState('2024');
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showAdvanceModal, setShowAdvanceModal] = useState(false);

  const months = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];

  const years = ['2024', '2023', '2022'];

  const filteredRecords = payrollRecords.filter(record => 
    record.month === selectedMonth && record.year.toString() === selectedYear
  );

  const totalBaseSalary = filteredRecords.reduce((sum, record) => sum + record.baseSalary, 0);
  const totalBonuses = filteredRecords.reduce((sum, record) => sum + record.bonuses, 0);
  const totalDeductions = filteredRecords.reduce((sum, record) => sum + record.deductions, 0);
  const totalAdvances = filteredRecords.reduce((sum, record) => sum + record.advances, 0);
  const totalNetSalary = filteredRecords.reduce((sum, record) => sum + record.netSalary, 0);

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR').format(amount) + ' AR';
  };

  const handleGeneratePayroll = () => {
    console.log(`Génération de la paie pour ${selectedMonth} ${selectedYear}`);
    setShowGenerateModal(false);
  };

  const handleDownloadSlip = (recordId: string) => {
    console.log(`Téléchargement du bulletin de paie ${recordId}`);
  };

  const handleMarkAsPaid = (recordId: string) => {
    setPayrollRecords(prev =>
      prev.map(record =>
        record.id === recordId ? { ...record, status: 'paid' as const } : record
      )
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Fiches de Paie et Avances</h1>
          <p className="text-gray-600 mt-1">Gestion de la paie des enseignants</p>
        </div>
        <div className="flex space-x-3 mt-4 sm:mt-0">
          <button
            onClick={() => setShowAdvanceModal(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <DollarSign className="h-4 w-4 mr-2" />
            Nouvelle Avance
          </button>
          <button
            onClick={() => setShowGenerateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90"
            style={{ backgroundColor: '#0a1186' }}
          >
            <Calculator className="h-4 w-4 mr-2" />
            Générer Paie
          </button>
        </div>
      </div>

      {/* Period Selection */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Mois</label>
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
            >
              {months.map(month => (
                <option key={month} value={month}>{month}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Année</label>
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={selectedYear}
              onChange={(e) => setSelectedYear(e.target.value)}
            >
              {years.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Salaires de Base</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{formatAmount(totalBaseSalary)}</p>
            </div>
            <div className="h-10 w-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <Briefcase className="h-5 w-5 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Primes</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{formatAmount(totalBonuses)}</p>
            </div>
            <div className="h-10 w-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <Plus className="h-5 w-5" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Retenues</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{formatAmount(totalDeductions)}</p>
            </div>
            <div className="h-10 w-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <Calculator className="h-5 w-5" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avances</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{formatAmount(totalAdvances)}</p>
            </div>
            <div className="h-10 w-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <DollarSign className="h-5 w-5 text-green-700" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Net</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{formatAmount(totalNetSalary)}</p>
            </div>
            <div className="h-10 w-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#c9cfcf' }}>
              <FileText className="h-5 w-5 text-gray-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Payroll Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead style={{ backgroundColor: '#0a1186' }}>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Enseignant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Salaire de Base
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Primes
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Retenues
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Avances
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Net à Payer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRecords.map((record) => (
                <tr key={record.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {record.teacherName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {record.month} {record.year}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatAmount(record.baseSalary)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatAmount(record.bonuses)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatAmount(record.deductions)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatAmount(record.advances)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {formatAmount(record.netSalary)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      record.status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {record.status === 'paid' ? 'Payé' : 'Brouillon'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleDownloadSlip(record.id)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Télécharger bulletin"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                      {record.status === 'draft' && (
                        <button
                          onClick={() => handleMarkAsPaid(record.id)}
                          className="text-green-600 hover:text-green-900 p-1 rounded"
                          title="Marquer comme payé"
                        >
                          <DollarSign className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Generate Payroll Modal */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Générer la Paie</h2>
              <button
                onClick={() => setShowGenerateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <form onSubmit={(e) => { e.preventDefault(); handleGeneratePayroll(); }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Période</label>
                <div className="grid grid-cols-2 gap-3">
                  <select
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(e.target.value)}
                  >
                    {months.map(month => (
                      <option key={month} value={month}>{month}</option>
                    ))}
                  </select>
                  <select
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(e.target.value)}
                  >
                    {years.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">
                  Cette action générera automatiquement les bulletins de paie pour tous les enseignants
                  en se basant sur leurs salaires de base et les éventuelles primes/retenues.
                </p>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowGenerateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  Générer
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Advance Modal */}
      {showAdvanceModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Nouvelle Avance</h2>
              <button
                onClick={() => setShowAdvanceModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Enseignant</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">Sélectionner un enseignant</option>
                  <option value="1">RAVELOJAONA Paul</option>
                  <option value="2">RAKOTO Marie</option>
                  <option value="3">DERA Toky</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Montant (AR)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Motif</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Raison de l'avance..."
                ></textarea>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAdvanceModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  Enregistrer
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}