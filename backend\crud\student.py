# crud/student.py
from typing import Optional, List
from sqlalchemy.orm import Session
from crud.base import CRUDBase
from models.student import Student
from schemas.student import StudentCreate, StudentUpdate

class CRUDStudent(CRUDBase[Student, StudentCreate, StudentUpdate]):
    def get_by_matricule(self, db: Session, *, matricule: str) -> Optional[Student]:
        return db.query(Student).filter(Student.matricule == matricule).first()
    
    def get_by_class(self, db: Session, *, classe_id: int) -> List[Student]:
        return db.query(Student).filter(Student.classe_id == classe_id).all()
    
    def get_active(self, db: Session) -> List[Student]:
        return db.query(Student).filter(Student.is_active == True).all()
    
    def search_by_name(self, db: Session, *, name: str) -> List[Student]:
        return db.query(Student).filter(
            (Student.nom.ilike(f"%{name}%")) | 
            (Student.prenom.ilike(f"%{name}%"))
        ).all()
    
    def get_by_parent_contact(self, db: Session, *, contact: str) -> List[Student]:
        return db.query(Student).filter(Student.contact_parent.ilike(f"%{contact}%")).all()

student = CRUDStudent(Student)
