# schemas/school_year.py
from pydantic import BaseModel, validator
from datetime import date
from typing import Optional

class SchoolYearBase(BaseModel):
    annee: str
    date_debut: date
    date_fin: date
    is_active: Optional[bool] = False

    @validator('annee')
    def validate_annee_format(cls, v):
        # Validate format like "2024-2025"
        if not v or len(v) != 9 or v[4] != '-':
            raise ValueError('Year format must be YYYY-YYYY (e.g., 2024-2025)')
        try:
            start_year = int(v[:4])
            end_year = int(v[5:])
            if end_year != start_year + 1:
                raise ValueError('End year must be start year + 1')
        except ValueError:
            raise ValueError('Invalid year format')
        return v

    @validator('date_fin')
    def validate_date_fin(cls, v, values):
        if 'date_debut' in values and v <= values['date_debut']:
            raise ValueError('End date must be after start date')
        return v

class SchoolYearCreate(SchoolYearBase):
    pass

class SchoolYearUpdate(BaseModel):
    annee: Optional[str] = None
    date_debut: Optional[date] = None
    date_fin: Optional[date] = None
    is_active: Optional[bool] = None

class SchoolYearRead(SchoolYearBase):
    id: int
    created_at: date

    class Config:
        from_attributes = True
