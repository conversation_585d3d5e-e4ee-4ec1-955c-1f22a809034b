# routes/timetables.py
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from utils.security import get_current_active_user, get_db
from crud.timetable import timetable
from schemas.timetable import (
    TimetableCreate, 
    TimetableRead, 
    TimetableUpdate, 
    TimetableWithDetails,
    DayOfWeekEnum
)
# from models.timetable import DayOfWeek  # No longer needed
from datetime import time

router = APIRouter(prefix="/api/timetables", tags=["Timetables"])

@router.get("/", response_model=List[TimetableWithDetails])
async def get_timetables(
    skip: int = 0,
    limit: int = 100,
    classe_id: Optional[int] = Query(None, description="Filter by class"),
    enseignant_id: Optional[int] = Query(None, description="Filter by teacher"),
    matiere_id: Optional[int] = Query(None, description="Filter by subject"),
    annee_scolaire_id: Optional[int] = Query(None, description="Filter by school year"),
    jour_semaine: Optional[DayOfWeekEnum] = Query(None, description="Filter by day of week"),
    salle: Optional[str] = Query(None, description="Filter by room"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all timetables with optional filters"""
    if any([classe_id, enseignant_id, matiere_id, annee_scolaire_id, jour_semaine, salle]):
        day_str = jour_semaine.value if jour_semaine else None
        return timetable.search_timetables(
            db,
            classe_id=classe_id,
            enseignant_id=enseignant_id,
            matiere_id=matiere_id,
            annee_scolaire_id=annee_scolaire_id,
            jour_semaine=day_str,
            salle=salle,
            skip=skip,
            limit=limit
        )
    return timetable.get_multi_with_details(db, skip=skip, limit=limit)

@router.get("/weekly-schedule", response_model=Dict[str, List[TimetableWithDetails]])
async def get_weekly_schedule(
    classe_id: Optional[int] = Query(None, description="Filter by class"),
    enseignant_id: Optional[int] = Query(None, description="Filter by teacher"),
    annee_scolaire_id: Optional[int] = Query(None, description="Filter by school year"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get weekly schedule organized by day"""
    return timetable.get_weekly_schedule(
        db,
        classe_id=classe_id,
        enseignant_id=enseignant_id,
        annee_scolaire_id=annee_scolaire_id
    )

@router.get("/by-class/{classe_id}", response_model=List[TimetableWithDetails])
async def get_timetables_by_class(
    classe_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all timetables for a specific class"""
    return timetable.get_by_class(db, classe_id=classe_id)

@router.get("/by-teacher/{enseignant_id}", response_model=List[TimetableWithDetails])
async def get_timetables_by_teacher(
    enseignant_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all timetables for a specific teacher"""
    return timetable.get_by_teacher(db, enseignant_id=enseignant_id)

@router.get("/by-subject/{matiere_id}", response_model=List[TimetableWithDetails])
async def get_timetables_by_subject(
    matiere_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all timetables for a specific subject"""
    return timetable.get_by_subject(db, matiere_id=matiere_id)

@router.get("/by-room/{salle}", response_model=List[TimetableWithDetails])
async def get_timetables_by_room(
    salle: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all timetables for a specific room"""
    return timetable.get_by_room(db, salle=salle)

@router.get("/{timetable_id}", response_model=TimetableWithDetails)
async def get_timetable(
    timetable_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific timetable by ID"""
    db_timetable = timetable.get_with_details(db, timetable_id)
    if not db_timetable:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Timetable entry not found"
        )
    return db_timetable

@router.post("/check-conflicts")
async def check_scheduling_conflicts(
    timetable_data: TimetableCreate,
    exclude_id: Optional[int] = Query(None, description="Exclude this timetable ID from conflict check"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Check for scheduling conflicts before creating/updating a timetable entry"""
    day_str = timetable_data.jour_semaine.value if hasattr(timetable_data.jour_semaine, 'value') else timetable_data.jour_semaine

    conflicts = {
        "teacher_conflicts": [],
        "class_conflicts": [],
        "room_conflicts": [],
        "has_conflicts": False
    }

    # Check teacher conflicts
    teacher_conflicts = timetable.check_teacher_conflict(
        db,
        enseignant_id=timetable_data.enseignant_id,
        jour_semaine=day_str,
        heure_debut=timetable_data.heure_debut,
        heure_fin=timetable_data.heure_fin,
        exclude_id=exclude_id
    )

    # Check class conflicts
    class_conflicts = timetable.check_class_conflict(
        db,
        classe_id=timetable_data.classe_id,
        jour_semaine=day_str,
        heure_debut=timetable_data.heure_debut,
        heure_fin=timetable_data.heure_fin,
        exclude_id=exclude_id
    )

    # Check room conflicts (if room is specified)
    room_conflicts = []
    if timetable_data.salle:
        room_conflicts = timetable.check_room_conflict(
            db,
            salle=timetable_data.salle,
            jour_semaine=day_str,
            heure_debut=timetable_data.heure_debut,
            heure_fin=timetable_data.heure_fin,
            exclude_id=exclude_id
        )
    
    conflicts["teacher_conflicts"] = [{"id": t.id, "details": f"Teacher already scheduled at {t.heure_debut}-{t.heure_fin}"} for t in teacher_conflicts]
    conflicts["class_conflicts"] = [{"id": t.id, "details": f"Class already scheduled at {t.heure_debut}-{t.heure_fin}"} for t in class_conflicts]
    conflicts["room_conflicts"] = [{"id": t.id, "details": f"Room already booked at {t.heure_debut}-{t.heure_fin}"} for t in room_conflicts]
    conflicts["has_conflicts"] = bool(teacher_conflicts or class_conflicts or room_conflicts)
    
    return conflicts

@router.post("/", response_model=TimetableRead)
async def create_timetable(
    timetable_in: TimetableCreate,
    force: bool = Query(False, description="Force creation even if conflicts exist"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new timetable entry"""
    # Check for conflicts unless forced
    if not force:
        day_str = timetable_in.jour_semaine.value if hasattr(timetable_in.jour_semaine, 'value') else timetable_in.jour_semaine

        # Check teacher conflicts
        teacher_conflicts = timetable.check_teacher_conflict(
            db,
            enseignant_id=timetable_in.enseignant_id,
            jour_semaine=day_str,
            heure_debut=timetable_in.heure_debut,
            heure_fin=timetable_in.heure_fin
        )

        # Check class conflicts
        class_conflicts = timetable.check_class_conflict(
            db,
            classe_id=timetable_in.classe_id,
            jour_semaine=day_str,
            heure_debut=timetable_in.heure_debut,
            heure_fin=timetable_in.heure_fin
        )
        
        if teacher_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Teacher is already scheduled at this time on {timetable_in.jour_semaine.value}"
            )
        
        if class_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Class is already scheduled at this time on {timetable_in.jour_semaine.value}"
            )
    
    return timetable.create(db, obj_in=timetable_in)

@router.put("/{timetable_id}", response_model=TimetableRead)
async def update_timetable(
    timetable_id: int,
    timetable_in: TimetableUpdate,
    force: bool = Query(False, description="Force update even if conflicts exist"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a timetable entry"""
    db_timetable = timetable.get(db, timetable_id)
    if not db_timetable:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Timetable entry not found"
        )
    
    # Check for conflicts if time-related fields are being updated
    if not force and any([
        timetable_in.enseignant_id is not None,
        timetable_in.classe_id is not None,
        timetable_in.jour_semaine is not None,
        timetable_in.heure_debut is not None,
        timetable_in.heure_fin is not None
    ]):
        # Use updated values or existing ones
        enseignant_id = timetable_in.enseignant_id or db_timetable.enseignant_id
        classe_id = timetable_in.classe_id or db_timetable.classe_id
        jour_semaine = (timetable_in.jour_semaine.value if hasattr(timetable_in.jour_semaine, 'value') else timetable_in.jour_semaine) if timetable_in.jour_semaine else db_timetable.jour_semaine
        heure_debut = timetable_in.heure_debut or db_timetable.heure_debut
        heure_fin = timetable_in.heure_fin or db_timetable.heure_fin
        
        # Check teacher conflicts
        teacher_conflicts = timetable.check_teacher_conflict(
            db,
            enseignant_id=enseignant_id,
            jour_semaine=jour_semaine,
            heure_debut=heure_debut,
            heure_fin=heure_fin,
            exclude_id=timetable_id
        )

        # Check class conflicts
        class_conflicts = timetable.check_class_conflict(
            db,
            classe_id=classe_id,
            jour_semaine=jour_semaine,
            heure_debut=heure_debut,
            heure_fin=heure_fin,
            exclude_id=timetable_id
        )
        
        if teacher_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Teacher is already scheduled at this time"
            )
        
        if class_conflicts:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Class is already scheduled at this time"
            )
    
    return timetable.update(db, db_obj=db_timetable, obj_in=timetable_in)

@router.delete("/{timetable_id}")
async def delete_timetable(
    timetable_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a timetable entry (soft delete by setting is_active to False)"""
    db_timetable = timetable.get(db, timetable_id)
    if not db_timetable:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Timetable entry not found"
        )
    
    # Soft delete by setting is_active to False
    timetable.update(db, db_obj=db_timetable, obj_in={"is_active": False})
    return {"message": "Timetable entry deactivated successfully"}
