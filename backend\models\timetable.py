# models/timetable.py
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Date, Time, Boolean, Text
from sqlalchemy.orm import relationship
from datetime import datetime, date, time
from db import Base
import enum

class DayOfWeek(enum.Enum):
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"

class Timetable(Base):
    __tablename__ = "timetables"

    id = Column(Integer, primary_key=True, index=True)
    classe_id = Column(Integer, ForeignKey("classes.id"), nullable=False)
    matiere_id = Column(Integer, ForeignKey("subjects.id"), nullable=False)
    enseignant_id = Column(Integer, ForeignKey("teachers.id"), nullable=False)
    annee_scolaire_id = Column(Integer, ForeignKey("school_years.id"), nullable=False)
    
    # Time slot information
    jour_semaine = Column(String, nullable=False)  # Day of the week
    heure_debut = Column(Time, nullable=False)  # Start time
    heure_fin = Column(Time, nullable=False)  # End time
    
    # Location and additional info
    salle = Column(String, nullable=True)  # Room/classroom
    description = Column(Text, nullable=True)  # Additional notes
    
    # Validity period
    date_debut = Column(Date, nullable=True)  # Start date for this schedule
    date_fin = Column(Date, nullable=True)  # End date for this schedule
    
    # Status
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    classe = relationship("Classe", back_populates="timetables")
    subject = relationship("Subject", back_populates="timetables")
    teacher = relationship("Teacher", back_populates="timetables")
    school_year = relationship("SchoolYear", back_populates="timetables")
