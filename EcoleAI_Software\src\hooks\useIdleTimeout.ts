import { useEffect, useRef, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { envConfig } from '../config/environment';

interface UseIdleTimeoutOptions {
    timeout?: number;
    onIdle?: () => void;
    onActive?: () => void;
    events?: string[];
}

export function useIdleTimeout(options: UseIdleTimeoutOptions = {}) {
    const { logout } = useAuth();
    const {
        timeout = envConfig.sessionTimeout,
        onIdle,
        onActive,
        events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    } = options;

    const timeoutRef = useRef<NodeJS.Timeout>();
    const isIdleRef = useRef(false);

    const resetTimeout = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        if (isIdleRef.current) {
            isIdleRef.current = false;
            onActive?.();
        }

        timeoutRef.current = setTimeout(() => {
            isIdleRef.current = true;
            onIdle?.();

            console.log('User idle timeout reached, logging out...');
            logout();
        }, timeout);
    }, [timeout, onIdle, onActive, logout]);

    useEffect(() => {
        events.forEach(event => {
            document.addEventListener(event, resetTimeout, true);
        });

        resetTimeout();

        return () => {
            events.forEach(event => {
                document.removeEventListener(event, resetTimeout, true);
            });

            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [events, resetTimeout]);

    return {
        isIdle: isIdleRef.current,
        resetTimeout
    };
}