# crud/subject.py
from typing import Optional, List
from sqlalchemy.orm import Session
from crud.base import CRUDBase
from models.subject import Subject
from schemas.subject import SubjectCreate, SubjectUpdate

class CRUDSubject(CRUDBase[Subject, SubjectCreate, SubjectUpdate]):
    def get_by_name(self, db: Session, *, nom: str) -> Optional[Subject]:
        return db.query(Subject).filter(Subject.nom.ilike(f"%{nom}%")).first()
    
    def search_by_name(self, db: Session, *, nom: str) -> List[Subject]:
        return db.query(Subject).filter(Subject.nom.ilike(f"%{nom}%")).all()

subject = CRUDSubject(Subject)
