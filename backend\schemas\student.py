# schemas/student.py
from pydantic import BaseModel, validator
from datetime import date, datetime
from typing import Optional

class StudentBase(BaseModel):
    matricule: str
    nom: str
    prenom: str
    date_naissance: date
    sexe: str
    adresse: Optional[str] = None
    nom_parent: str
    contact_parent: str
    classe_id: int
    is_active: Optional[bool] = True

    @validator('matricule')
    def validate_matricule(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('Student ID must be at least 5 characters long')
        return v.strip().upper()

    @validator('nom', 'prenom', 'nom_parent')
    def validate_names(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Name must be at least 2 characters long')
        return v.strip().title()

    @validator('sexe')
    def validate_sexe(cls, v):
        if v not in ['M', 'F']:
            raise ValueError('Gender must be M or F')
        return v.upper()

    @validator('date_naissance')
    def validate_date_naissance(cls, v):
        today = date.today()
        age = today.year - v.year - ((today.month, today.day) < (v.month, v.day))
        if age < 3 or age > 25:
            raise ValueError('Student age must be between 3 and 25 years')
        return v

    @validator('contact_parent')
    def validate_contact_parent(cls, v):
        if not v or len(v.strip()) < 8:
            raise ValueError('Parent contact must be at least 8 characters long')
        return v.strip()

class StudentCreate(StudentBase):
    pass

class StudentUpdate(BaseModel):
    matricule: Optional[str] = None
    nom: Optional[str] = None
    prenom: Optional[str] = None
    date_naissance: Optional[date] = None
    sexe: Optional[str] = None
    adresse: Optional[str] = None
    nom_parent: Optional[str] = None
    contact_parent: Optional[str] = None
    classe_id: Optional[int] = None
    is_active: Optional[bool] = None

class StudentRead(StudentBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
