import { useSession } from './useSession';
import { useActivityTracker } from './useActivityTracker';
import { useAuth } from '../contexts/AuthContext';

export function useEnhancedSession() {
  const session = useSession();
  const { getActivityStats } = useActivityTracker();
  const { logout } = useAuth();

  const getSessionHealth = () => {
    const activity = getActivityStats();
    const { timeUntilExpiry, isExpiringSoon } = session;

    return {
      ...session,
      ...activity,
      healthScore: calculateHealthScore(session, activity),
      recommendations: getRecommendations(session, activity)
    };
  };

  const calculateHealthScore = (sessionData: any, activityData: any) => {
    let score = 100;

    // Deduct points for session age
    if (sessionData.sessionDuration > 2 * 60 * 60 * 1000) { // 2 hours
      score -= 20;
    }

    // Deduct points for inactivity
    if (activityData.timeSinceLastActivity > 10 * 60 * 1000) { // 10 minutes
      score -= 30;
    }

    // Deduct points for approaching expiry
    if (sessionData.isExpiringSoon) {
      score -= 25;
    }

    return Math.max(0, score);
  };

  const getRecommendations = (sessionData: any, activityData: any) => {
    const recommendations = [];

    if (sessionData.isExpiringSoon) {
      recommendations.push('Session expiring soon - consider refreshing');
    }

    if (activityData.timeSinceLastActivity > 15 * 60 * 1000) {
      recommendations.push('Long period of inactivity detected');
    }

    if (sessionData.sessionDuration > 3 * 60 * 60 * 1000) {
      recommendations.push('Long session - consider taking a break');
    }

    return recommendations;
  };

  return {
    ...session,
    getSessionHealth,
    forceLogout: logout
  };
}
