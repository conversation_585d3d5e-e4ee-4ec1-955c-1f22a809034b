from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional

class UserBase(BaseModel):
    username: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    role: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None
    created_at: Optional[datetime] = None

class UserInDB(UserBase):
    hashed_password: str

class UserRead(UserBase):
    id: int

    class Config:
        from_attributes = True

class UserCreate(UserBase):
    username: str
    email: EmailStr
    password: str