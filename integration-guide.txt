# React Authentication API Integration Guide
# École AI - Complete Authentication Implementation Guide

## Overview
This guide provides step-by-step instructions to integrate your React frontend with the existing FastAPI authentication backend. The backend already implements JWT-based authentication with refresh tokens, user registration, and protected routes.

## Current State Analysis

### Backend API Endpoints (Already Implemented)
- POST /auth/token - Login endpoint (returns access_token, refresh_token)
- POST /auth/register - User registration
- POST /auth/refresh - Refresh access token
- GET /users/me - Get current user profile

### Frontend Current State
- Basic AuthContext with mock authentication
- Login component with form handling
- Protected routes implementation
- User state management with localStorage

## Step 1: Install Required Dependencies

Add these packages to your React project:

```bash
npm install axios
npm install @types/node  # For TypeScript support
```

## Step 2: Create API Configuration

Create a new file: `src/config/api.ts`

```typescript
// src/config/api.ts
import axios from 'axios';

// API Configuration
export const API_BASE_URL = 'http://localhost:8000'; // Adjust to your backend URL
export const API_ENDPOINTS = {
  LOGIN: '/auth/token',
  REGISTER: '/auth/register',
  REFRESH: '/auth/refresh',
  USER_PROFILE: '/users/me',
};

// Create axios instance with default configuration
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken
          });
          
          const { access_token } = response.data;
          localStorage.setItem('access_token', access_token);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('ecole-ai-user');
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);
```

## Step 3: Create Authentication Service

Create a new file: `src/services/authService.ts`

```typescript
// src/services/authService.ts
import { apiClient, API_ENDPOINTS } from '../config/api';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  full_name: string;
  phone?: string;
  role?: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  phone?: string;
  role?: string;
  disabled?: boolean;
  created_at?: string;
}

export class AuthService {
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // FastAPI expects form data for OAuth2PasswordRequestForm
    const formData = new FormData();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    
    const response = await apiClient.post(API_ENDPOINTS.LOGIN, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    
    return response.data;
  }

  static async register(userData: RegisterData): Promise<User> {
    const response = await apiClient.post(API_ENDPOINTS.REGISTER, userData);
    return response.data;
  }

  static async getCurrentUser(): Promise<User> {
    const response = await apiClient.get(API_ENDPOINTS.USER_PROFILE);
    return response.data;
  }

  static async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await apiClient.post(API_ENDPOINTS.REFRESH, {
      refresh_token: refreshToken
    });
    return response.data;
  }

  static logout(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('ecole-ai-user');
  }
}
```

## Step 4: Update AuthContext

Replace the content of `src/contexts/AuthContext.tsx`:

```typescript
// src/contexts/AuthContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { AuthService, User } from '../services/authService';

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    const accessToken = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');
    
    if (accessToken && refreshToken) {
      try {
        // Verify token by fetching user profile
        const userData = await AuthService.getCurrentUser();
        setUser(userData);
      } catch (error) {
        // Token invalid, try refresh
        try {
          const authResponse = await AuthService.refreshToken(refreshToken);
          localStorage.setItem('access_token', authResponse.access_token);
          
          const userData = await AuthService.getCurrentUser();
          setUser(userData);
        } catch (refreshError) {
          // Refresh failed, clear tokens
          AuthService.logout();
        }
      }
    }
    
    setIsLoading(false);
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const authResponse = await AuthService.login({ username, password });
      
      // Store tokens
      localStorage.setItem('access_token', authResponse.access_token);
      localStorage.setItem('refresh_token', authResponse.refresh_token);
      
      // Fetch user profile
      const userData = await AuthService.getCurrentUser();
      setUser(userData);
      
      // Store user data for offline access
      localStorage.setItem('ecole-ai-user', JSON.stringify(userData));
      
      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const logout = () => {
    AuthService.logout();
    setUser(null);
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{ 
      user, 
      login, 
      logout, 
      isLoading, 
      isAuthenticated 
    }}>
      {children}
    </AuthContext.Provider>
  );
}
```

## Step 5: Error Handling Patterns

Create `src/utils/errorHandler.ts`:

```typescript
// src/utils/errorHandler.ts
import axios from 'axios';

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export class ErrorHandler {
  static handleAuthError(error: unknown): ApiError {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const detail = error.response?.data?.detail;
      
      switch (status) {
        case 401:
          return {
            message: detail || 'Identifiants incorrects',
            status: 401,
            code: 'UNAUTHORIZED'
          };
        case 400:
          return {
            message: detail || 'Données invalides',
            status: 400,
            code: 'BAD_REQUEST'
          };
        case 422:
          return {
            message: 'Données de validation incorrectes',
            status: 422,
            code: 'VALIDATION_ERROR'
          };
        case 500:
          return {
            message: 'Erreur serveur interne',
            status: 500,
            code: 'SERVER_ERROR'
          };
        default:
          return {
            message: detail || 'Une erreur est survenue',
            status: status || 0,
            code: 'UNKNOWN_ERROR'
          };
      }
    }
    
    return {
      message: 'Erreur de connexion réseau',
      code: 'NETWORK_ERROR'
    };
  }
}

## Step 6: Update Login Component

Update your `src/components/Login.tsx` to use the new authentication service:

```typescript
// src/components/Login.tsx - Updated handleSubmit function
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setError('');
  setIsLoading(true);

  try {
    const success = await login(username, password);
    if (!success) {
      setError('Identifiants incorrects. Veuillez réessayer.');
    }
  } catch (err) {
    const apiError = ErrorHandler.handleAuthError(err);
    setError(apiError.message);
  } finally {
    setIsLoading(false);
  }
};
```

## Step 7: Create Protected Route Component

Create `src/components/ProtectedRoute.tsx`:

```typescript
// src/components/ProtectedRoute.tsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  requireAuth?: boolean;
}

export default function ProtectedRoute({
  children,
  allowedRoles = [],
  requireAuth = true
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role || '')) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}
```

## Step 8: Security Best Practices

### Token Storage Security
```typescript
// src/utils/tokenStorage.ts
export class SecureTokenStorage {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static setTokens(accessToken: string, refreshToken: string): void {
    // In production, consider using httpOnly cookies or secure storage
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem('ecole-ai-user');
  }

  static hasValidTokens(): boolean {
    return !!(this.getAccessToken() && this.getRefreshToken());
  }
}
```

### Environment Configuration
Create `.env` file in your React project root:

```env
# .env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_NAME=École AI
VITE_TOKEN_REFRESH_THRESHOLD=300000
```

Update your API configuration:

```typescript
// src/config/api.ts - Updated
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
```

## Step 9: Advanced Authentication Features

### Auto-logout on Token Expiration
```typescript
// src/hooks/useTokenExpiration.ts
import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

export function useTokenExpiration() {
  const { logout } = useAuth();

  useEffect(() => {
    const checkTokenExpiration = () => {
      const token = localStorage.getItem('access_token');
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Date.now() / 1000;

          if (payload.exp < currentTime) {
            logout();
          }
        } catch (error) {
          console.error('Error checking token expiration:', error);
          logout();
        }
      }
    };

    // Check every 5 minutes
    const interval = setInterval(checkTokenExpiration, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [logout]);
}
```

### Session Management Hook
```typescript
// src/hooks/useSession.ts
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

export function useSession() {
  const { user, isAuthenticated } = useAuth();
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      const token = localStorage.getItem('access_token');
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          setSessionExpiry(new Date(payload.exp * 1000));
        } catch (error) {
          console.error('Error parsing token:', error);
        }
      }
    } else {
      setSessionExpiry(null);
    }
  }, [isAuthenticated]);

  return {
    user,
    isAuthenticated,
    sessionExpiry,
    timeUntilExpiry: sessionExpiry ? sessionExpiry.getTime() - Date.now() : null
  };
}
```

## Step 10: Testing Authentication Flow

### Test Login Function
```typescript
// src/utils/authTest.ts
export async function testAuthFlow() {
  try {
    // Test login
    const loginResult = await AuthService.login({
      username: 'admin',
      password: 'admin123'
    });
    console.log('Login successful:', loginResult);

    // Test getting user profile
    const user = await AuthService.getCurrentUser();
    console.log('User profile:', user);

    // Test token refresh
    const refreshResult = await AuthService.refreshToken(loginResult.refresh_token);
    console.log('Token refresh successful:', refreshResult);

  } catch (error) {
    console.error('Auth test failed:', error);
  }
}
```

## Step 11: Implementation Checklist

### Frontend Changes Required:
1. ✅ Install axios dependency
2. ✅ Create API configuration with interceptors
3. ✅ Create authentication service
4. ✅ Update AuthContext to use real API
5. ✅ Update Login component error handling
6. ✅ Create ProtectedRoute component
7. ✅ Add environment configuration
8. ✅ Implement token storage utilities
9. ✅ Add session management hooks

### Backend Verification:
1. ✅ Ensure backend is running on correct port
2. ✅ Verify CORS configuration allows frontend origin
3. ✅ Test API endpoints with Postman/curl
4. ✅ Confirm database is properly initialized

### Security Considerations:
1. ✅ Use HTTPS in production
2. ✅ Implement proper CORS policy
3. ✅ Consider httpOnly cookies for token storage
4. ✅ Add rate limiting to login endpoint
5. ✅ Implement proper error logging
6. ✅ Use environment variables for sensitive config

## Step 12: Common Issues and Solutions

### CORS Issues
If you encounter CORS errors, add this to your FastAPI backend:

```python
# backend/main.py
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Vite default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Token Refresh Issues
- Ensure refresh token is stored securely
- Handle network errors during refresh
- Implement exponential backoff for failed requests

### Form Data vs JSON
- Login endpoint expects FormData (OAuth2PasswordRequestForm)
- Other endpoints expect JSON
- Ensure correct Content-Type headers

## Next Steps After Implementation

1. Test the complete authentication flow
2. Implement user registration form
3. Add password reset functionality
4. Implement role-based access control
5. Add audit logging for authentication events
6. Consider implementing 2FA for enhanced security

## Production Deployment Notes

1. Use environment variables for all configuration
2. Implement proper logging and monitoring
3. Use HTTPS for all communications
4. Consider using httpOnly cookies instead of localStorage
5. Implement proper session timeout handling
6. Add comprehensive error tracking

This guide provides a complete implementation path from your current mock authentication to a production-ready JWT-based authentication system integrated with your FastAPI backend.
```
