import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, CheckCircle } from 'lucide-react';
import { apiClient } from '../../../config/api';

interface TimetableModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (timetable: any) => void;
  editingTimetable?: any;
}

interface FormData {
  classe_id: number;
  matiere_id: number;
  enseignant_id: number;
  annee_scolaire_id: number;
  jour_semaine: string;
  heure_debut: string;
  heure_fin: string;
  salle: string;
  description: string;
  is_active: boolean;
}

interface FormErrors {
  [key: string]: string;
}

interface Class {
  id: number;
  nom: string;
  niveau: string;
  annee_scolaire_id: number;
}

interface Subject {
  id: number;
  nom: string;
  coef: number;
}

interface Teacher {
  id: number;
  nom: string;
  prenom: string;
  specialite?: string;
  is_active: boolean;
}

interface SchoolYear {
  id: number;
  annee: string;
  is_active: boolean;
}

interface ConflictCheck {
  has_conflicts: boolean;
  teacher_conflicts: Array<{ id: number; details: string }>;
  class_conflicts: Array<{ id: number; details: string }>;
  room_conflicts: Array<{ id: number; details: string }>;
}

const DAYS_OF_WEEK = [
  { value: 'monday', label: 'Lundi' },
  { value: 'tuesday', label: 'Mardi' },
  { value: 'wednesday', label: 'Mercredi' },
  { value: 'thursday', label: 'Jeudi' },
  { value: 'friday', label: 'Vendredi' },
  { value: 'saturday', label: 'Samedi' },
  { value: 'sunday', label: 'Dimanche' }
];

export default function TimetableModal({ isOpen, onClose, onSuccess, editingTimetable }: TimetableModalProps) {
  const [formData, setFormData] = useState<FormData>({
    classe_id: 0,
    matiere_id: 0,
    enseignant_id: 0,
    annee_scolaire_id: 0,
    jour_semaine: 'monday',
    heure_debut: '08:00',
    heure_fin: '09:00',
    salle: '',
    description: '',
    is_active: true
  });

  const [classes, setClasses] = useState<Class[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [schoolYears, setSchoolYears] = useState<SchoolYear[]>([]);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  const [conflicts, setConflicts] = useState<ConflictCheck | null>(null);
  const [showConflictWarning, setShowConflictWarning] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchData();
      if (editingTimetable) {
        setFormData({
          classe_id: editingTimetable.classe_id,
          matiere_id: editingTimetable.matiere_id,
          enseignant_id: editingTimetable.enseignant_id,
          annee_scolaire_id: editingTimetable.annee_scolaire_id,
          jour_semaine: editingTimetable.jour_semaine,
          heure_debut: editingTimetable.heure_debut,
          heure_fin: editingTimetable.heure_fin,
          salle: editingTimetable.salle || '',
          description: editingTimetable.description || '',
          is_active: editingTimetable.is_active
        });
      } else {
        resetForm();
      }
    }
  }, [isOpen, editingTimetable]);

  const fetchData = async () => {
    try {
      const [classesRes, subjectsRes, teachersRes, schoolYearsRes] = await Promise.all([
        apiClient.get('/api/classes/'),
        apiClient.get('/api/subjects/'),
        apiClient.get('/api/teachers/'),
        apiClient.get('/api/school-years/')
      ]);

      setClasses(classesRes.data);
      setSubjects(subjectsRes.data);
      setTeachers(teachersRes.data);
      setSchoolYears(schoolYearsRes.data);

      // Set default school year to active one
      const activeSchoolYear = schoolYearsRes.data.find((sy: SchoolYear) => sy.is_active);
      if (activeSchoolYear && !editingTimetable) {
        setFormData(prev => ({ ...prev, annee_scolaire_id: activeSchoolYear.id }));
      }
    } catch (err) {
      console.error('Error fetching data:', err);
    }
  };

  const resetForm = () => {
    const activeSchoolYear = schoolYears.find(sy => sy.is_active);
    setFormData({
      classe_id: 0,
      matiere_id: 0,
      enseignant_id: 0,
      annee_scolaire_id: activeSchoolYear?.id || 0,
      jour_semaine: 'monday',
      heure_debut: '08:00',
      heure_fin: '09:00',
      salle: '',
      description: '',
      is_active: true
    });
    setFormErrors({});
    setConflicts(null);
    setShowConflictWarning(false);
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.classe_id) errors.classe_id = 'La classe est requise';
    if (!formData.matiere_id) errors.matiere_id = 'La matière est requise';
    if (!formData.enseignant_id) errors.enseignant_id = 'L\'enseignant est requis';
    if (!formData.annee_scolaire_id) errors.annee_scolaire_id = 'L\'année scolaire est requise';
    if (!formData.heure_debut) errors.heure_debut = 'L\'heure de début est requise';
    if (!formData.heure_fin) errors.heure_fin = 'L\'heure de fin est requise';

    if (formData.heure_debut && formData.heure_fin && formData.heure_debut >= formData.heure_fin) {
      errors.heure_fin = 'L\'heure de fin doit être après l\'heure de début';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const checkConflicts = async () => {
    if (!validateForm()) return;

    try {
      setCheckingConflicts(true);
      const response = await apiClient.post('/api/timetables/check-conflicts', formData, {
        params: editingTimetable ? { exclude_id: editingTimetable.id } : {}
      });

      setConflicts(response.data);
      setShowConflictWarning(response.data.has_conflicts);
    } catch (err) {
      console.error('Error checking conflicts:', err);
    } finally {
      setCheckingConflicts(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent, forceCreate = false) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Check conflicts first if not forcing
    if (!forceCreate && !conflicts) {
      await checkConflicts();
      return;
    }

    try {
      setLoading(true);

      let response;
      if (editingTimetable) {
        response = await apiClient.put(`/api/timetables/${editingTimetable.id}`, formData, {
          params: forceCreate ? { force: true } : {}
        });
      } else {
        response = await apiClient.post('/api/timetables/', formData, {
          params: forceCreate ? { force: true } : {}
        });
      }

      onSuccess(response.data);
      onClose();
      resetForm();
    } catch (err: any) {
      console.error('Error saving timetable:', err);
      if (err.response?.status === 409) {
        // Conflict detected, show warning
        setShowConflictWarning(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-screen overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            {editingTimetable ? 'Modifier le créneau' : 'Nouveau créneau'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Conflict Warning */}
        {showConflictWarning && conflicts?.has_conflicts && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-yellow-800 mb-2">
                  Conflits détectés
                </h3>
                <div className="text-sm text-yellow-700 space-y-1">
                  {conflicts.teacher_conflicts.map((conflict, index) => (
                    <div key={index}>• Enseignant: {conflict.details}</div>
                  ))}
                  {conflicts.class_conflicts.map((conflict, index) => (
                    <div key={index}>• Classe: {conflict.details}</div>
                  ))}
                  {conflicts.room_conflicts.map((conflict, index) => (
                    <div key={index}>• Salle: {conflict.details}</div>
                  ))}
                </div>
                <div className="mt-3 flex space-x-2">
                  <button
                    onClick={(e) => handleSubmit(e, true)}
                    disabled={loading}
                    className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 disabled:opacity-50"
                  >
                    Forcer la création
                  </button>
                  <button
                    onClick={() => setShowConflictWarning(false)}
                    className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
                  >
                    Modifier
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Classe */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Classe *
              </label>
              <select
                value={formData.classe_id}
                onChange={(e) => setFormData({ ...formData, classe_id: parseInt(e.target.value) })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.classe_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                required
              >
                <option value={0}>Sélectionner une classe</option>
                {classes.map(classe => (
                  <option key={classe.id} value={classe.id}>
                    {classe.nom} ({classe.niveau})
                  </option>
                ))}
              </select>
              {formErrors.classe_id && (
                <p className="text-red-500 text-xs mt-1">{formErrors.classe_id}</p>
              )}
            </div>

            {/* Matière */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Matière *
              </label>
              <select
                value={formData.matiere_id}
                onChange={(e) => setFormData({ ...formData, matiere_id: parseInt(e.target.value) })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.matiere_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                required
              >
                <option value={0}>Sélectionner une matière</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>
                    {subject.nom} (Coef: {subject.coef})
                  </option>
                ))}
              </select>
              {formErrors.matiere_id && (
                <p className="text-red-500 text-xs mt-1">{formErrors.matiere_id}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Enseignant */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Enseignant *
              </label>
              <select
                value={formData.enseignant_id}
                onChange={(e) => setFormData({ ...formData, enseignant_id: parseInt(e.target.value) })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.enseignant_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                required
              >
                <option value={0}>Sélectionner un enseignant</option>
                {teachers.filter(t => t.is_active).map(teacher => (
                  <option key={teacher.id} value={teacher.id}>
                    {teacher.prenom} {teacher.nom} {teacher.specialite ? `(${teacher.specialite})` : ''}
                  </option>
                ))}
              </select>
              {formErrors.enseignant_id && (
                <p className="text-red-500 text-xs mt-1">{formErrors.enseignant_id}</p>
              )}
            </div>

            {/* Année scolaire */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Année scolaire *
              </label>
              <select
                value={formData.annee_scolaire_id}
                onChange={(e) => setFormData({ ...formData, annee_scolaire_id: parseInt(e.target.value) })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.annee_scolaire_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                required
              >
                <option value={0}>Sélectionner une année</option>
                {schoolYears.map(year => (
                  <option key={year.id} value={year.id}>
                    {year.annee} {year.is_active ? '(Active)' : ''}
                  </option>
                ))}
              </select>
              {formErrors.annee_scolaire_id && (
                <p className="text-red-500 text-xs mt-1">{formErrors.annee_scolaire_id}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Jour de la semaine */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Jour de la semaine *
              </label>
              <select
                value={formData.jour_semaine}
                onChange={(e) => setFormData({ ...formData, jour_semaine: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                {DAYS_OF_WEEK.map(day => (
                  <option key={day.value} value={day.value}>
                    {day.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Heure de début */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Heure de début *
              </label>
              <input
                type="time"
                value={formData.heure_debut}
                onChange={(e) => setFormData({ ...formData, heure_debut: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.heure_debut ? 'border-red-500' : 'border-gray-300'
                  }`}
                required
              />
              {formErrors.heure_debut && (
                <p className="text-red-500 text-xs mt-1">{formErrors.heure_debut}</p>
              )}
            </div>

            {/* Heure de fin */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Heure de fin *
              </label>
              <input
                type="time"
                value={formData.heure_fin}
                onChange={(e) => setFormData({ ...formData, heure_fin: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.heure_fin ? 'border-red-500' : 'border-gray-300'
                  }`}
                required
              />
              {formErrors.heure_fin && (
                <p className="text-red-500 text-xs mt-1">{formErrors.heure_fin}</p>
              )}
            </div>
          </div>

          {/* Salle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Salle
            </label>
            <input
              type="text"
              value={formData.salle}
              onChange={(e) => setFormData({ ...formData, salle: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Ex: Salle A1, Laboratoire..."
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              placeholder="Notes ou informations supplémentaires..."
            />
          </div>

          {/* Statut actif */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
              Créneau actif
            </label>
          </div>

          {/* Boutons */}
          <div className="flex justify-between pt-4">
            <div className="flex space-x-2">
              {!showConflictWarning && (
                <button
                  type="button"
                  onClick={checkConflicts}
                  disabled={checkingConflicts || loading}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  {checkingConflicts ? (
                    <>
                      <Loader className="h-4 w-4 animate-spin mr-2" />
                      Vérification...
                    </>
                  ) : (
                    <>
                      <Clock className="h-4 w-4 mr-2" />
                      Vérifier les conflits
                    </>
                  )}
                </button>
              )}

              {conflicts && !conflicts.has_conflicts && (
                <div className="inline-flex items-center px-3 py-2 text-green-700 bg-green-50 rounded-lg">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Aucun conflit
                </div>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading || checkingConflicts}
                className="px-4 py-2 text-white rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                style={{ backgroundColor: '#0a1186' }}
              >
                {loading ? (
                  <div className="flex items-center">
                    <Loader className="h-4 w-4 animate-spin mr-2" />
                    Enregistrement...
                  </div>
                ) : (
                  editingTimetable ? 'Modifier' : 'Créer'
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
