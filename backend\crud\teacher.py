# crud/teacher.py
from typing import Optional, List
from sqlalchemy.orm import Session
from crud.base import CRUDBase
from models.teacher import Teacher
from schemas.teacher import TeacherCreate, TeacherUpdate

class CRUDTeacher(CRUDBase[Teacher, TeacherCreate, TeacherUpdate]):
    def get_by_email(self, db: Session, *, email: str) -> Optional[Teacher]:
        return db.query(Teacher).filter(Teacher.email == email).first()
    
    def get_active(self, db: Session) -> List[Teacher]:
        return db.query(Teacher).filter(Teacher.is_active == True).all()
    
    def search_by_name(self, db: Session, *, name: str) -> List[Teacher]:
        return db.query(Teacher).filter(
            (Teacher.nom.ilike(f"%{name}%")) | 
            (Teacher.prenom.ilike(f"%{name}%"))
        ).all()
    
    def get_by_specialite(self, db: Session, *, specialite: str) -> List[Teacher]:
        return db.query(Teacher).filter(Teacher.specialite.ilike(f"%{specialite}%")).all()

teacher = CRUDTeacher(Teacher)
