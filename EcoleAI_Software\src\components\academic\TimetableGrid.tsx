import React, { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, User, BookOpen, ChevronLeft, ChevronRight, Filter } from 'lucide-react';
import { apiClient } from '../../config/api';

interface Timetable {
  id: number;
  classe_id: number;
  matiere_id: number;
  enseignant_id: number;
  jour_semaine: string;
  heure_debut: string;
  heure_fin: string;
  salle?: string;
  description?: string;
  classe?: {
    id: number;
    nom: string;
    niveau: string;
  };
  subject?: {
    id: number;
    nom: string;
    coef: number;
  };
  teacher?: {
    id: number;
    nom: string;
    prenom: string;
    specialite?: string;
  };
}

interface Class {
  id: number;
  nom: string;
  niveau: string;
}

interface Teacher {
  id: number;
  nom: string;
  prenom: string;
  specialite?: string;
}

const DAYS_OF_WEEK = [
  { value: 'monday', label: 'Lundi', short: 'Lun' },
  { value: 'tuesday', label: 'Mar<PERSON>', short: 'Mar' },
  { value: 'wednesday', label: 'Mercredi', short: 'Mer' },
  { value: 'thursday', label: 'Je<PERSON>', short: 'Jeu' },
  { value: 'friday', label: 'Vendredi', short: 'Ven' },
  { value: 'saturday', label: 'Samedi', short: 'Sam' },
  { value: 'sunday', label: 'Dimanche', short: 'Dim' }
];

const TIME_SLOTS = [
  '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
  '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
  '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',
  '17:00', '17:30', '18:00'
];

const SUBJECT_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280'  // Gray
];

export default function TimetableGrid() {
  const [timetables, setTimetables] = useState<Timetable[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedTeacher, setSelectedTeacher] = useState<string>('');
  const [viewMode, setViewMode] = useState<'class' | 'teacher'>('class');
  const [currentWeek, setCurrentWeek] = useState(new Date());

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchTimetables();
  }, [selectedClass, selectedTeacher, viewMode]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [classesRes, teachersRes] = await Promise.all([
        apiClient.get('/api/classes/'),
        apiClient.get('/api/teachers/')
      ]);

      setClasses(classesRes.data);
      setTeachers(teachersRes.data);

      // Set default selection
      if (classesRes.data.length > 0) {
        setSelectedClass(classesRes.data[0].id.toString());
      }
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const fetchTimetables = async () => {
    try {
      let url = '/api/timetables/weekly-schedule?';

      if (viewMode === 'class' && selectedClass) {
        url += `classe_id=${selectedClass}`;
      } else if (viewMode === 'teacher' && selectedTeacher) {
        url += `enseignant_id=${selectedTeacher}`;
      }

      const response = await apiClient.get(url);

      // Convert the weekly schedule object to a flat array
      const allTimetables: Timetable[] = [];
      Object.values(response.data).forEach((dayTimetables: any) => {
        allTimetables.push(...dayTimetables);
      });

      setTimetables(allTimetables);
    } catch (err) {
      console.error('Error fetching timetables:', err);
      setError('Erreur lors du chargement de l\'emploi du temps');
    }
  };

  const getSubjectColor = (subjectId: number): string => {
    return SUBJECT_COLORS[subjectId % SUBJECT_COLORS.length];
  };

  const formatTime = (timeString: string): string => {
    return timeString.slice(0, 5);
  };

  const getTimeSlotPosition = (time: string): number => {
    const timeIndex = TIME_SLOTS.indexOf(time);
    return timeIndex >= 0 ? timeIndex : 0;
  };

  const getTimeSlotHeight = (startTime: string, endTime: string): number => {
    const startIndex = getTimeSlotPosition(startTime);
    const endIndex = getTimeSlotPosition(endTime);
    return Math.max(1, endIndex - startIndex);
  };

  const getTimetableForDay = (day: string): Timetable[] => {
    return timetables.filter(t => t.jour_semaine === day);
  };

  const renderTimetableEntry = (timetable: Timetable) => {
    const color = getSubjectColor(timetable.matiere_id);
    const height = getTimeSlotHeight(timetable.heure_debut, timetable.heure_fin);

    return (
      <div
        key={timetable.id}
        className="absolute left-0 right-0 mx-1 rounded-lg p-2 text-white text-xs shadow-sm cursor-pointer hover:shadow-md transition-shadow"
        style={{
          backgroundColor: color,
          top: `${getTimeSlotPosition(timetable.heure_debut) * 40}px`,
          height: `${height * 40 - 2}px`,
          minHeight: '38px'
        }}
        title={`${timetable.subject?.nom} - ${timetable.teacher?.prenom} ${timetable.teacher?.nom}`}
      >
        <div className="font-medium truncate">
          {timetable.subject?.nom}
        </div>
        <div className="text-xs opacity-90 truncate">
          {formatTime(timetable.heure_debut)} - {formatTime(timetable.heure_fin)}
        </div>
        {viewMode === 'teacher' && timetable.classe && (
          <div className="text-xs opacity-90 truncate">
            {timetable.classe.nom}
          </div>
        )}
        {viewMode === 'class' && timetable.teacher && (
          <div className="text-xs opacity-90 truncate">
            {timetable.teacher.prenom} {timetable.teacher.nom}
          </div>
        )}
        {timetable.salle && (
          <div className="text-xs opacity-90 truncate flex items-center">
            <MapPin className="h-3 w-3 mr-1" />
            {timetable.salle}
          </div>
        )}
      </div>
    );
  };

  const getWeekDates = (date: Date) => {
    const week = [];
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfWeek.setDate(diff);

    for (let i = 0; i < 7; i++) {
      const weekDate = new Date(startOfWeek);
      weekDate.setDate(startOfWeek.getDate() + i);
      week.push(weekDate);
    }
    return week;
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeek = new Date(currentWeek);
    newWeek.setDate(currentWeek.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentWeek(newWeek);
  };

  const weekDates = getWeekDates(currentWeek);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Chargement de l'emploi du temps...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Emploi du Temps</h1>
          <p className="text-gray-600 mt-1">Vue calendrier hebdomadaire</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Controls */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* View Mode */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mode d'affichage
            </label>
            <div className="flex space-x-2">
              <button
                onClick={() => setViewMode('class')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${viewMode === 'class'
                  ? 'text-white'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
                  }`}
                style={viewMode === 'class' ? { backgroundColor: '#0a1186' } : {}}
              >
                Par Classe
              </button>
              <button
                onClick={() => setViewMode('teacher')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${viewMode === 'teacher'
                  ? 'text-white'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
                  }`}
                style={viewMode === 'teacher' ? { backgroundColor: '#0a1186' } : {}}
              >
                Par Enseignant
              </button>
            </div>
          </div>

          {/* Class/Teacher Selection */}
          {viewMode === 'class' ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Classe
              </label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Sélectionner une classe</option>
                {classes.map(classe => (
                  <option key={classe.id} value={classe.id.toString()}>
                    {classe.nom} ({classe.niveau})
                  </option>
                ))}
              </select>
            </div>
          ) : (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enseignant
              </label>
              <select
                value={selectedTeacher}
                onChange={(e) => setSelectedTeacher(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Sélectionner un enseignant</option>
                {teachers.filter(t => t.is_active).map(teacher => (
                  <option key={teacher.id} value={teacher.id.toString()}>
                    {teacher.prenom} {teacher.nom}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Week Navigation */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Semaine
            </label>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigateWeek('prev')}
                className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <div className="flex-1 text-center">
                <span className="text-sm font-medium">
                  {weekDates[0].toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })} - {' '}
                  {weekDates[6].toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' })}
                </span>
              </div>
              <button
                onClick={() => navigateWeek('next')}
                className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Timetable Grid */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="overflow-x-auto">
          <div className="min-w-full">
            {/* Header with days */}
            <div className="grid grid-cols-8 border-b border-gray-200">
              <div className="p-4 bg-gray-50 border-r border-gray-200">
                <div className="text-sm font-medium text-gray-700">Horaires</div>
              </div>
              {DAYS_OF_WEEK.slice(0, 7).map((day, index) => (
                <div key={day.value} className="p-4 bg-gray-50 border-r border-gray-200 last:border-r-0">
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">{day.label}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {weekDates[index].toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Time slots and schedule */}
            <div className="grid grid-cols-8 relative">
              {/* Time column */}
              <div className="border-r border-gray-200">
                {TIME_SLOTS.map((time, index) => (
                  <div
                    key={time}
                    className="h-10 px-4 py-2 border-b border-gray-100 bg-gray-50 flex items-center"
                    style={{ height: '40px' }}
                  >
                    <span className="text-xs text-gray-600 font-medium">{time}</span>
                  </div>
                ))}
              </div>

              {/* Days columns */}
              {DAYS_OF_WEEK.slice(0, 7).map((day) => (
                <div key={day.value} className="border-r border-gray-200 last:border-r-0 relative">
                  {/* Time slot backgrounds */}
                  {TIME_SLOTS.map((time, index) => (
                    <div
                      key={time}
                      className="h-10 border-b border-gray-100 hover:bg-gray-50"
                      style={{ height: '40px' }}
                    />
                  ))}

                  {/* Timetable entries */}
                  <div className="absolute inset-0">
                    {getTimetableForDay(day.value).map(renderTimetableEntry)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Empty state */}
        {timetables.length === 0 && (selectedClass || selectedTeacher) && (
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun cours programmé</h3>
            <p className="text-gray-600">
              {viewMode === 'class'
                ? 'Aucun cours n\'est programmé pour cette classe cette semaine.'
                : 'Aucun cours n\'est programmé pour cet enseignant cette semaine.'
              }
            </p>
          </div>
        )}

        {/* No selection state */}
        {!selectedClass && !selectedTeacher && (
          <div className="text-center py-12">
            <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Sélectionnez un filtre</h3>
            <p className="text-gray-600">
              Choisissez une classe ou un enseignant pour afficher l'emploi du temps.
            </p>
          </div>
        )}
      </div>

      {/* Legend */}
      {timetables.length > 0 && (
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Légende</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from(new Set(timetables.map(t => t.subject?.nom))).filter(Boolean).map((subjectName, index) => {
              const subject = timetables.find(t => t.subject?.nom === subjectName)?.subject;
              if (!subject) return null;

              return (
                <div key={subject.id} className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: getSubjectColor(subject.id) }}
                  />
                  <span className="text-sm text-gray-700">{subject.nom}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Statistics */}
      {timetables.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Heures/Semaine</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">
                  {timetables.reduce((total, t) => {
                    const start = new Date(`2000-01-01T${t.heure_debut}`);
                    const end = new Date(`2000-01-01T${t.heure_fin}`);
                    return total + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
                  }, 0).toFixed(1)}h
                </p>
              </div>
              <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3f2fd' }}>
                <Clock className="h-6 w-6" style={{ color: '#0a1186' }} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Matières Enseignées</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">
                  {new Set(timetables.map(t => t.matiere_id)).size}
                </p>
              </div>
              <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#f3e5f5' }}>
                <BookOpen className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  {viewMode === 'class' ? 'Enseignants' : 'Classes'}
                </p>
                <p className="text-2xl font-bold text-gray-900 mt-1">
                  {viewMode === 'class'
                    ? new Set(timetables.map(t => t.enseignant_id)).size
                    : new Set(timetables.map(t => t.classe_id)).size
                  }
                </p>
              </div>
              <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e8f5e8' }}>
                <User className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
