import React, { useState } from 'react';
import { Database, Download, Upload, Clock, CheckCircle, AlertCircle, Calendar, Settings, Play } from 'lucide-react';

interface BackupRecord {
  id: string;
  name: string;
  size: string;
  date: string;
  type: 'manual' | 'auto';
  status: 'success' | 'failed' | 'in_progress';
  description?: string;
}

const mockBackups: BackupRecord[] = [
  {
    id: '1',
    name: 'backup_ecole_ai_20240115_143022.sql',
    size: '45.2 MB',
    date: '2024-01-15T14:30:22Z',
    type: 'auto',
    status: 'success',
    description: 'Sauvegarde automatique quotidienne'
  },
  {
    id: '2',
    name: 'backup_ecole_ai_20240114_120000.sql',
    size: '44.8 MB',
    date: '2024-01-14T12:00:00Z',
    type: 'auto',
    status: 'success',
    description: 'Sauvegarde automatique quotidienne'
  },
  {
    id: '3',
    name: 'backup_manuel_avant_migration.sql',
    size: '43.9 MB',
    date: '2024-01-13T09:15:00Z',
    type: 'manual',
    status: 'success',
    description: 'Sauvegarde avant migration des données'
  },
  {
    id: '4',
    name: 'backup_ecole_ai_20240112_120000.sql',
    size: '44.1 MB',
    date: '2024-01-12T12:00:00Z',
    type: 'auto',
    status: 'failed',
    description: 'Échec de la sauvegarde automatique'
  }
];

export default function Backup() {
  const [backups, setBackups] = useState<BackupRecord[]>(mockBackups);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState<BackupRecord | null>(null);

  const [backupSettings, setBackupSettings] = useState({
    autoBackup: true,
    frequency: 'daily',
    time: '12:00',
    retention: 30,
    compression: true
  });

  const handleCreateBackup = async () => {
    setIsBackingUp(true);
    
    // Simulation de la création de sauvegarde
    setTimeout(() => {
      const newBackup: BackupRecord = {
        id: Date.now().toString(),
        name: `backup_manuel_${new Date().toISOString().replace(/[:.-]/g, '_')}.sql`,
        size: '45.7 MB',
        date: new Date().toISOString(),
        type: 'manual',
        status: 'success',
        description: 'Sauvegarde manuelle créée par l\'administrateur'
      };
      
      setBackups(prev => [newBackup, ...prev]);
      setIsBackingUp(false);
    }, 3000);
  };

  const handleDownloadBackup = (backup: BackupRecord) => {
    console.log(`Téléchargement de ${backup.name}`);
    // Simulation du téléchargement
  };

  const handleDeleteBackup = (backupId: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette sauvegarde ?')) {
      setBackups(prev => prev.filter(backup => backup.id !== backupId));
    }
  };

  const handleRestoreBackup = (backup: BackupRecord) => {
    setSelectedBackup(backup);
    setShowRestoreModal(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-yellow-500 animate-spin" />;
      default:
        return null;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sauvegarde et Restauration</h1>
          <p className="text-gray-600 mt-1">Gestion des sauvegardes de la base de données</p>
        </div>
        <div className="flex space-x-3 mt-4 sm:mt-0">
          <button
            onClick={() => setShowScheduleModal(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Settings className="h-4 w-4 mr-2" />
            Paramètres
          </button>
          <button
            onClick={handleCreateBackup}
            disabled={isBackingUp}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 disabled:opacity-50"
            style={{ backgroundColor: '#0a1186' }}
          >
            {isBackingUp ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Sauvegarde en cours...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                Créer une Sauvegarde
              </>
            )}
          </button>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Sauvegardes</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{backups.length}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <Database className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Dernière Sauvegarde</p>
              <p className="text-sm font-bold text-gray-900 mt-1">
                {backups.length > 0 ? formatDate(backups[0].date) : 'Aucune'}
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <Clock className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Espace Utilisé</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">1.2 GB</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <Upload className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Sauvegarde Auto</p>
              <p className="text-sm font-bold mt-1" style={{ color: backupSettings.autoBackup ? '#10B981' : '#EF4444' }}>
                {backupSettings.autoBackup ? 'Activée' : 'Désactivée'}
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <Calendar className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Backup Settings Overview */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration Actuelle</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <p className="text-sm font-medium text-gray-600">Fréquence</p>
            <p className="text-lg font-semibold text-gray-900 mt-1">
              {backupSettings.frequency === 'daily' ? 'Quotidienne' :
               backupSettings.frequency === 'weekly' ? 'Hebdomadaire' : 'Mensuelle'}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Heure</p>
            <p className="text-lg font-semibold text-gray-900 mt-1">{backupSettings.time}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Rétention</p>
            <p className="text-lg font-semibold text-gray-900 mt-1">{backupSettings.retention} jours</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Compression</p>
            <p className="text-lg font-semibold text-gray-900 mt-1">
              {backupSettings.compression ? 'Activée' : 'Désactivée'}
            </p>
          </div>
        </div>
      </div>

      {/* Backups List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Historique des Sauvegardes</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead style={{ backgroundColor: '#0a1186' }}>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Nom du Fichier
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Taille
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {backups.map((backup) => (
                <tr key={backup.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{backup.name}</div>
                      {backup.description && (
                        <div className="text-sm text-gray-500">{backup.description}</div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {backup.size}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(backup.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      backup.type === 'auto' 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {backup.type === 'auto' ? 'Automatique' : 'Manuelle'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(backup.status)}
                      <span className={`ml-2 text-sm ${
                        backup.status === 'success' ? 'text-green-600' :
                        backup.status === 'failed' ? 'text-red-600' :
                        'text-yellow-600'
                      }`}>
                        {backup.status === 'success' ? 'Réussie' :
                         backup.status === 'failed' ? 'Échec' : 'En cours'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {backup.status === 'success' && (
                        <>
                          <button
                            onClick={() => handleDownloadBackup(backup)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded"
                            title="Télécharger"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleRestoreBackup(backup)}
                            className="text-green-600 hover:text-green-900 p-1 rounded"
                            title="Restaurer"
                          >
                            <Play className="h-4 w-4" />
                          </button>
                        </>
                      )}
                      <button
                        onClick={() => handleDeleteBackup(backup.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="Supprimer"
                      >
                        <AlertCircle className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Schedule Settings Modal */}
      {showScheduleModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Paramètres de Sauvegarde</h2>
              <button
                onClick={() => setShowScheduleModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <form className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Sauvegarde Automatique</h3>
                  <p className="text-sm text-gray-500">Créer automatiquement des sauvegardes selon un planning</p>
                </div>
                <input
                  type="checkbox"
                  checked={backupSettings.autoBackup}
                  onChange={(e) => setBackupSettings(prev => ({ ...prev, autoBackup: e.target.checked }))}
                  className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              {backupSettings.autoBackup && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Fréquence</label>
                      <select
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        value={backupSettings.frequency}
                        onChange={(e) => setBackupSettings(prev => ({ ...prev, frequency: e.target.value }))}
                      >
                        <option value="daily">Quotidienne</option>
                        <option value="weekly">Hebdomadaire</option>
                        <option value="monthly">Mensuelle</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Heure</label>
                      <input
                        type="time"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        value={backupSettings.time}
                        onChange={(e) => setBackupSettings(prev => ({ ...prev, time: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Durée de rétention (jours)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="365"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={backupSettings.retention}
                      onChange={(e) => setBackupSettings(prev => ({ ...prev, retention: parseInt(e.target.value) }))}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Les sauvegardes plus anciennes seront supprimées automatiquement
                    </p>
                  </div>
                </>
              )}

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Compression</h3>
                  <p className="text-sm text-gray-500">Compresser les fichiers de sauvegarde pour économiser l'espace</p>
                </div>
                <input
                  type="checkbox"
                  checked={backupSettings.compression}
                  onChange={(e) => setBackupSettings(prev => ({ ...prev, compression: e.target.checked }))}
                  className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-6">
                <button
                  type="button"
                  onClick={() => setShowScheduleModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  type="button"
                  onClick={() => setShowScheduleModal(false)}
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  Enregistrer
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Restore Modal */}
      {showRestoreModal && selectedBackup && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Restaurer la Sauvegarde</h2>
              <button
                onClick={() => {
                  setShowRestoreModal(false);
                  setSelectedBackup(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mr-3" />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800">Attention</h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      Cette action va remplacer toutes les données actuelles par celles de la sauvegarde.
                      Cette opération est irréversible.
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900">Détails de la sauvegarde :</h4>
                <div className="mt-2 space-y-1 text-sm text-gray-600">
                  <p><strong>Fichier :</strong> {selectedBackup.name}</p>
                  <p><strong>Date :</strong> {formatDate(selectedBackup.date)}</p>
                  <p><strong>Taille :</strong> {selectedBackup.size}</p>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="confirm-restore"
                  className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                />
                <label htmlFor="confirm-restore" className="ml-2 text-sm text-gray-700">
                  Je comprends que cette action est irréversible
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-6">
              <button
                onClick={() => {
                  setShowRestoreModal(false);
                  setSelectedBackup(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                className="px-4 py-2 bg-red-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Restaurer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}