# schemas/grade.py
from pydantic import BaseModel, validator
from datetime import date, datetime
from typing import Optional

class GradeBase(BaseModel):
    eleve_id: int
    cours_id: int
    note: float
    type: str
    date: Optional[date] = None
    commentaire: Optional[str] = None

    @validator('note')
    def validate_note(cls, v):
        if v < 0 or v > 20:
            raise ValueError('Grade must be between 0 and 20')
        return v

    @validator('type')
    def validate_type(cls, v):
        valid_types = ['DS', 'Exam', 'Test', 'Homework', 'Quiz', 'Project', 'Oral']
        if v not in valid_types:
            raise ValueError(f'Grade type must be one of: {", ".join(valid_types)}')
        return v

class GradeCreate(GradeBase):
    pass

class GradeUpdate(BaseModel):
    eleve_id: Optional[int] = None
    cours_id: Optional[int] = None
    note: Optional[float] = None
    type: Optional[str] = None
    date: Optional[date] = None
    commentaire: Optional[str] = None

class GradeRead(GradeBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
