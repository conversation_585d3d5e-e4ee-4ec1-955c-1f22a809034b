# École AI - Frontend Hierarchical Structure

## Overview

The frontend has been restructured to implement a comprehensive hierarchical menu system for student enrollment and school management. This document outlines the new structure and components.

## New Hierarchical Navigation Structure

### Main Sections

1. **Tableau de bord** - Dashboard (single item)
2. **Gestion Académique** - Academic Management
3. **Gestion des Élèves** - Student Management ⭐ (Primary Focus)
4. **Gestion Financière** - Financial Management
5. **Administration Système** - System Administration

### Student Management (Gestion des Élèves)

This is the primary focus area with the complete student enrollment workflow:

- **Inscription d'Élèves** (`/students/enrollment`) - Student Enrollment Wizard
- **Répertoire des Élèves** (`/students/directory`) - Student Directory
- **Rapports Académiques** (`/students/academic-reports`) - Academic Reports
- **Analyses de Performance** (`/students/performance`) - Performance Analytics
- **Absences** (`/students/absences`) - Attendance Management

## Student Enrollment Wizard

### Components Structure

```
src/components/students/enrollment/
├── StudentEnrollmentWizard.tsx      # Main wizard component
├── PrerequisitesCheck.tsx           # Step 1: Prerequisites verification
├── StudentRegistrationForm.tsx      # Step 2: Student information form
├── FinancialSetup.tsx              # Step 3: Financial configuration
├── AcademicAssignment.tsx          # Step 4: Academic assignment
└── EnrollmentSummary.tsx           # Step 5: Final review and submission
```

### Enrollment Workflow Steps

#### Step 1: Prerequisites Check (`PrerequisitesCheck.tsx`)
- **Purpose**: Verify system configuration before enrollment
- **Checks**:
  - Active school year exists
  - Classes are available for current year
  - Subjects are configured
  - Active teachers are available
- **API Endpoints**:
  - `GET /api/school-years/`
  - `GET /api/classes/`
  - `GET /api/subjects/`
  - `GET /api/teachers/?active_only=true`

#### Step 2: Student Registration (`StudentRegistrationForm.tsx`)
- **Purpose**: Collect student personal and academic information
- **Data Collection**:
  - Personal info: name, birth date, gender, address
  - Parent info: parent name, contact information
  - Academic: class assignment
  - System: auto-generated matricule
- **API Endpoints**:
  - `GET /api/students/generate-matricule`
  - `GET /api/classes/` (for class selection)
- **Validation**: Real-time form validation with backend checks

#### Step 3: Financial Setup (`FinancialSetup.tsx`)
- **Purpose**: Configure fees and payment structure
- **Features**:
  - Registration fee setup
  - Monthly recurring fees
  - Additional fees (books, uniform, transport, etc.)
  - Payment method selection
- **Fee Types**: registration, monthly_fee, books, uniform, transport, cafeteria, activities, exam_fee, other

#### Step 4: Academic Assignment (`AcademicAssignment.tsx`)
- **Purpose**: Review and confirm academic assignments
- **Features**:
  - Display selected class information
  - Show assigned courses and teachers
  - Course schedule overview
  - Teacher specialties matching
- **Note**: Currently simulates course assignments (backend courses API needs implementation)

#### Step 5: Enrollment Summary (`EnrollmentSummary.tsx`)
- **Purpose**: Final review and submission
- **Features**:
  - Complete enrollment summary
  - Financial overview
  - Final submission to backend
  - Success confirmation with student ID
- **API Endpoints**:
  - `POST /api/students/` (create student)
  - `POST /api/payments/` (create payment records)

## Backend Integration

### API Endpoints Used

| Endpoint | Method | Purpose | Component |
|----------|--------|---------|-----------|
| `/api/school-years/` | GET | Get school years | PrerequisitesCheck |
| `/api/classes/` | GET | Get available classes | PrerequisitesCheck, StudentRegistrationForm |
| `/api/subjects/` | GET | Get subjects | PrerequisitesCheck, AcademicAssignment |
| `/api/teachers/` | GET | Get teachers | PrerequisitesCheck, AcademicAssignment |
| `/api/students/generate-matricule` | GET | Generate unique student ID | StudentRegistrationForm |
| `/api/students/` | POST | Create new student | EnrollmentSummary |
| `/api/students/` | GET | Search/list students | StudentDirectory |
| `/api/payments/` | POST | Create payment records | EnrollmentSummary |

### Data Flow

1. **Prerequisites** → Load system configuration data
2. **Student Form** → Collect and validate student information
3. **Financial Setup** → Configure payment structure
4. **Academic Assignment** → Review course assignments
5. **Final Submission** → Create student and payment records

## File Organization

### New Structure
```
src/components/
├── students/
│   ├── enrollment/           # Enrollment wizard components
│   │   ├── StudentEnrollmentWizard.tsx
│   │   ├── PrerequisitesCheck.tsx
│   │   ├── StudentRegistrationForm.tsx
│   │   ├── FinancialSetup.tsx
│   │   ├── AcademicAssignment.tsx
│   │   └── EnrollmentSummary.tsx
│   └── StudentDirectory.tsx  # Student directory/listing
├── academic/                 # Academic management (placeholders)
│   └── SchoolYearManagement.tsx
├── financial/               # Financial management (placeholders)
│   └── FinancialDashboard.tsx
└── [existing components]    # Legacy components preserved
```

### Legacy Components

Legacy components have been preserved but commented out in the routing:
- `Students.tsx` → Replaced by `StudentDirectory.tsx`
- `StudentList.tsx` → Integrated into new student management
- Individual management pages → Reorganized into hierarchical structure

## Navigation Features

### Hierarchical Menu (`Layout.tsx`)
- **Expandable Groups**: Click to expand/collapse sections
- **Active State Tracking**: Highlights current section and active items
- **Role-based Access**: Shows only items accessible to user role
- **Default Expansion**: Student Management section expanded by default

### Navigation States
- **Single Items**: Direct navigation (e.g., Dashboard)
- **Group Items**: Expandable with child items
- **Active Highlighting**: Blue highlighting for active sections
- **Breadcrumb Logic**: Path-based active state detection

## Usage Instructions

### For Administrators

1. **Navigate to Student Enrollment**:
   - Go to "Gestion des Élèves" → "Inscription d'Élèves"
   - Or directly visit `/students/enrollment`

2. **Follow the Enrollment Wizard**:
   - Step through each section systematically
   - Complete all required information
   - Review before final submission

3. **View Enrolled Students**:
   - Go to "Gestion des Élèves" → "Répertoire des Élèves"
   - Search, filter, and manage existing students

### For Developers

1. **Adding New Enrollment Steps**:
   - Create component in `src/components/students/enrollment/`
   - Add to steps array in `StudentEnrollmentWizard.tsx`
   - Implement required props interface

2. **Extending Navigation**:
   - Modify `hierarchicalNavigation` in `Layout.tsx`
   - Add new routes in `App.tsx`
   - Create corresponding components

3. **Backend Integration**:
   - Use existing API service patterns
   - Add error handling and loading states
   - Implement proper TypeScript interfaces

## Future Enhancements

### Planned Features
- **Bulk Student Import**: CSV/Excel import functionality
- **Document Upload**: Student document management
- **ID Card Generation**: Automatic student ID card creation
- **Parent Notifications**: Email/SMS notifications
- **Course Management**: Full course assignment system
- **Academic Calendar**: Integration with school calendar

### Technical Improvements
- **Form Persistence**: Save draft enrollments
- **Offline Support**: Work without internet connection
- **Advanced Validation**: More sophisticated form validation
- **Audit Trail**: Track enrollment changes
- **Reporting**: Enrollment statistics and reports

## Testing

### Manual Testing Checklist
- [ ] Navigation between all menu items works
- [ ] Enrollment wizard completes successfully
- [ ] Form validation works correctly
- [ ] Backend integration functions properly
- [ ] Student directory displays and filters correctly
- [ ] Role-based access control works

### API Testing
- [ ] All API endpoints respond correctly
- [ ] Error handling works for failed requests
- [ ] Data validation works on backend
- [ ] Student creation and payment creation succeed

## Troubleshooting

### Common Issues
1. **Navigation not working**: Check route definitions in `App.tsx`
2. **API errors**: Verify backend is running and endpoints are accessible
3. **Form validation issues**: Check schema validation in components
4. **Missing data**: Ensure prerequisites are properly configured

### Debug Mode
- Enable development mode for detailed logging
- Check browser console for errors
- Use React Developer Tools for component inspection
