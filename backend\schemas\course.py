# schemas/course.py
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class CourseBase(BaseModel):
    enseignant_id: int
    matiere_id: int
    classe_id: int
    horaire: Optional[str] = None

class CourseCreate(CourseBase):
    pass

class CourseUpdate(BaseModel):
    enseignant_id: Optional[int] = None
    matiere_id: Optional[int] = None
    classe_id: Optional[int] = None
    horaire: Optional[str] = None

class CourseRead(CourseBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
