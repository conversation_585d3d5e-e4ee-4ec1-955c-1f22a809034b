# schemas/teacher.py
from pydantic import BaseModel, EmailStr, validator
from datetime import date, datetime
from typing import Optional

class TeacherBase(BaseModel):
    nom: str
    prenom: str
    specialite: Optional[str] = None
    email: Optional[EmailStr] = None
    telephone: Optional[str] = None
    date_embauche: Optional[date] = None
    is_active: Optional[bool] = True

    @validator('nom', 'prenom')
    def validate_names(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Name must be at least 2 characters long')
        return v.strip().title()

    @validator('telephone')
    def validate_telephone(cls, v):
        if v and len(v.strip()) < 8:
            raise ValueError('Phone number must be at least 8 characters long')
        return v.strip() if v else None

class TeacherCreate(TeacherBase):
    pass

class TeacherUpdate(BaseModel):
    nom: Optional[str] = None
    prenom: Optional[str] = None
    specialite: Optional[str] = None
    email: Optional[EmailStr] = None
    telephone: Optional[str] = None
    date_embauche: Optional[date] = None
    is_active: Optional[bool] = None

class TeacherRead(TeacherBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
