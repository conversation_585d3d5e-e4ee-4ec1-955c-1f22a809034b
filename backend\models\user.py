# models/user.py
from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime
from datetime import datetime
from db import Base
from sqlalchemy.orm import relationship

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True)
    phone = Column(String, nullable=True)
    role = Column(String, nullable=True)
    full_name = Column(String)
    disabled = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    hashed_password = Column(String, nullable=False)

    refresh_tokens = relationship("RefreshToken", back_populates="user")
