import React, { useState } from 'react';
import { X, BookO<PERSON>, AlertCircle } from 'lucide-react';
import { apiClient } from '../../../../config/api';

interface SubjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (subject: any) => void;
}

interface FormData {
  nom: string;
  description: string;
  coef: number;
}

interface FormErrors {
  [key: string]: string;
}

export default function SubjectModal({ isOpen, onClose, onSuccess }: SubjectModalProps) {
  const [formData, setFormData] = useState<FormData>({
    nom: '',
    description: '',
    coef: 1
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Le nom de la matière est requis';
    }

    if (formData.coef < 0.5 || formData.coef > 10) {
      errors.coef = 'Le coefficient doit être entre 0.5 et 10';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const response = await apiClient.post('/api/subjects/', formData);
      onSuccess(response.data);
      onClose();
      resetForm();
    } catch (error: any) {
      console.error('Error creating subject:', error);
      
      if (error.response?.status === 422 && error.response?.data) {
        const backendErrors = error.response.data;
        const newFormErrors: FormErrors = {};
        
        if (backendErrors.nom) {
          newFormErrors.nom = Array.isArray(backendErrors.nom) ? backendErrors.nom[0] : backendErrors.nom;
        }
        if (backendErrors.coef) {
          newFormErrors.coef = Array.isArray(backendErrors.coef) ? backendErrors.coef[0] : backendErrors.coef;
        }
        if (backendErrors.description) {
          newFormErrors.description = Array.isArray(backendErrors.description) ? backendErrors.description[0] : backendErrors.description;
        }
        
        setFormErrors(newFormErrors);
      } else {
        setFormErrors({ general: 'Erreur lors de la création de la matière' });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      nom: '',
      description: '',
      coef: 1
    });
    setFormErrors({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-1">
              Nouvelle matière
            </h2>
            <p className="text-sm text-gray-600">
              Créez une nouvelle matière
            </p>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
            type="button"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {formErrors.general && (
            <div className="flex items-center p-3 text-red-700 bg-red-100 border border-red-300 rounded-md">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span className="text-sm">{formErrors.general}</span>
            </div>
          )}

          {/* Nom de la matière */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom de la matière *
            </label>
            <input
              type="text"
              value={formData.nom}
              onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.nom 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="Ex: Mathématiques"
            />
            {formErrors.nom && (
              <p className="mt-1 text-sm text-red-600">{formErrors.nom}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.description 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              rows={3}
              placeholder="Description de la matière (optionnel)"
            />
            {formErrors.description && (
              <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>
            )}
          </div>

          {/* Coefficient */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Coefficient *
            </label>
            <input
              type="number"
              min="0.5"
              max="10"
              step="0.5"
              value={formData.coef}
              onChange={(e) => setFormData({ ...formData, coef: parseFloat(e.target.value) || 1 })}
              className={`w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 ${
                formErrors.coef 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              placeholder="1"
            />
            {formErrors.coef && (
              <p className="mt-1 text-sm text-red-600">{formErrors.coef}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Coefficient entre 0.5 et 10
            </p>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200"
              disabled={isLoading}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-200 hover:opacity-90 disabled:opacity-50"
              style={{ backgroundColor: '#0a1186' }}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </div>
              ) : (
                'Créer'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
