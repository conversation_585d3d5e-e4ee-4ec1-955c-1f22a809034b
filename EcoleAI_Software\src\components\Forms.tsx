import React, { useState } from 'react';
import { FileEdit, Plus, Eye, Copy, Trash2, <PERSON><PERSON><PERSON>, Users, BarChart } from 'lucide-react';

interface FormField {
  id: string;
  type: 'text' | 'email' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date';
  label: string;
  placeholder?: string;
  required: boolean;
  options?: string[];
}

interface CustomForm {
  id: string;
  title: string;
  description: string;
  fields: FormField[];
  targetAudience: 'students' | 'parents' | 'teachers';
  status: 'draft' | 'published' | 'closed';
  createdDate: string;
  responses: number;
}

const mockForms: CustomForm[] = [
  {
    id: '1',
    title: 'Enquête de Satisfaction Cantine',
    description: 'Évaluation de la qualité des repas servis à la cantine scolaire',
    fields: [
      { id: '1', type: 'select', label: 'Comment évaluez-vous la qualité des repas?', required: true, options: ['Excellent', 'Bon', 'Moyen', '<PERSON>uva<PERSON>'] },
      { id: '2', type: 'textarea', label: 'Suggestions d\'amélioration', required: false },
      { id: '3', type: 'checkbox', label: 'Régimes alimentaires', required: false, options: ['Végétarien', 'Sans gluten', 'Halal'] }
    ],
    targetAudience: 'students',
    status: 'published',
    createdDate: '2024-01-10',
    responses: 145
  },
  {
    id: '2',
    title: 'Réunion Parents-Professeurs',
    description: 'Inscription aux créneaux de rendez-vous pour les entretiens',
    fields: [
      { id: '1', type: 'select', label: 'Créneau souhaité', required: true, options: ['8h-9h', '9h-10h', '10h-11h', '14h-15h', '15h-16h'] },
      { id: '2', type: 'text', label: 'Matière prioritaire à discuter', required: false },
      { id: '3', type: 'textarea', label: 'Points spécifiques à aborder', required: false }
    ],
    targetAudience: 'parents',
    status: 'published',
    createdDate: '2024-01-05',
    responses: 89
  }
];

export default function Forms() {
  const [forms, setForms] = useState<CustomForm[]>(mockForms);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedForm, setSelectedForm] = useState<CustomForm | null>(null);
  const [formBuilder, setFormBuilder] = useState<{
    title: string;
    description: string;
    targetAudience: 'students' | 'parents' | 'teachers';
    fields: FormField[];
  }>({
    title: '',
    description: '',
    targetAudience: 'students',
    fields: []
  });

  const fieldTypes = [
    { value: 'text', label: 'Texte court' },
    { value: 'textarea', label: 'Texte long' },
    { value: 'email', label: 'Email' },
    { value: 'number', label: 'Nombre' },
    { value: 'date', label: 'Date' },
    { value: 'select', label: 'Liste déroulante' },
    { value: 'checkbox', label: 'Cases à cocher' },
    { value: 'radio', label: 'Choix unique' }
  ];

  const handleAddField = () => {
    const newField: FormField = {
      id: Date.now().toString(),
      type: 'text',
      label: 'Nouveau champ',
      required: false
    };
    setFormBuilder(prev => ({
      ...prev,
      fields: [...prev.fields, newField]
    }));
  };

  const handleUpdateField = (fieldId: string, updates: Partial<FormField>) => {
    setFormBuilder(prev => ({
      ...prev,
      fields: prev.fields.map(field =>
        field.id === fieldId ? { ...field, ...updates } : field
      )
    }));
  };

  const handleRemoveField = (fieldId: string) => {
    setFormBuilder(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== fieldId)
    }));
  };

  const handleSaveForm = () => {
    const newForm: CustomForm = {
      id: Date.now().toString(),
      title: formBuilder.title,
      description: formBuilder.description,
      fields: formBuilder.fields,
      targetAudience: formBuilder.targetAudience,
      status: 'draft',
      createdDate: new Date().toISOString().split('T')[0],
      responses: 0
    };

    setForms(prev => [newForm, ...prev]);
    setShowCreateModal(false);
    setFormBuilder({ title: '', description: '', targetAudience: 'students', fields: [] });
  };

  const handleDeleteForm = (formId: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce formulaire ?')) {
      setForms(prev => prev.filter(form => form.id !== formId));
    }
  };

  const handleDuplicateForm = (form: CustomForm) => {
    const duplicatedForm: CustomForm = {
      ...form,
      id: Date.now().toString(),
      title: form.title + ' (Copie)',
      status: 'draft',
      createdDate: new Date().toISOString().split('T')[0],
      responses: 0
    };
    setForms(prev => [duplicatedForm, ...prev]);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Formulaires Personnalisés</h1>
          <p className="text-gray-600 mt-1">Création et gestion des questionnaires</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 transition-opacity"
          style={{ backgroundColor: '#0a1186' }}
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouveau Formulaire
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Formulaires</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{forms.length}</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1186' }}>
              <FileEdit className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Publiés</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {forms.filter(f => f.status === 'published').length}
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#ffdd5a' }}>
              <Eye className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Réponses Totales</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {forms.reduce((sum, form) => sum + form.responses, 0)}
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#a1ecff' }}>
              <Users className="h-6 w-6" style={{ color: '#0a1186' }} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Taux Moyen</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">87.3%</p>
            </div>
            <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#e3ea9c' }}>
              <BarChart className="h-6 w-6 text-green-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Forms List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Mes Formulaires</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {forms.map((form) => (
            <div key={form.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h4 className="text-lg font-medium text-gray-900">{form.title}</h4>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      form.status === 'published' ? 'bg-green-100 text-green-800' :
                      form.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {form.status === 'published' ? 'Publié' : 
                       form.status === 'draft' ? 'Brouillon' : 'Fermé'}
                    </span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                          style={{ backgroundColor: '#a1ecff', color: '#0a1186' }}>
                      {form.targetAudience === 'students' ? 'Élèves' :
                       form.targetAudience === 'parents' ? 'Parents' : 'Professeurs'}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-gray-600">{form.description}</p>
                  <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span>Créé le {new Date(form.createdDate).toLocaleDateString('fr-FR')}</span>
                    <span>•</span>
                    <span>{form.fields.length} champs</span>
                    <span>•</span>
                    <span className="font-medium text-gray-700">{form.responses} réponses</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setSelectedForm(form);
                      setShowPreviewModal(true);
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    title="Prévisualiser"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDuplicateForm(form)}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    title="Dupliquer"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    title="Paramètres"
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteForm(form.id)}
                    className="p-2 text-red-400 hover:text-red-600 rounded-full hover:bg-red-50"
                    title="Supprimer"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Créer un Nouveau Formulaire</h2>
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  setFormBuilder({ title: '', description: '', targetAudience: 'students', fields: [] });
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <div className="space-y-6">
              {/* Form Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Titre du formulaire</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={formBuilder.title}
                    onChange={(e) => setFormBuilder(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Entrez le titre du formulaire"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Public cible</label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={formBuilder.targetAudience}
                    onChange={(e) => setFormBuilder(prev => ({ ...prev, targetAudience: e.target.value as any }))}
                  >
                    <option value="students">Élèves</option>
                    <option value="parents">Parents</option>
                    <option value="teachers">Professeurs</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  value={formBuilder.description}
                  onChange={(e) => setFormBuilder(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Décrivez l'objectif de ce formulaire"
                ></textarea>
              </div>

              {/* Form Builder */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Champs du formulaire</h3>
                  <button
                    onClick={handleAddField}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter un champ
                  </button>
                </div>

                <div className="space-y-4">
                  {formBuilder.fields.map((field, index) => (
                    <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Type de champ</label>
                          <select
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            value={field.type}
                            onChange={(e) => handleUpdateField(field.id, { type: e.target.value as any })}
                          >
                            {fieldTypes.map(type => (
                              <option key={type.value} value={type.value}>{type.label}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Label</label>
                          <input
                            type="text"
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            value={field.label}
                            onChange={(e) => handleUpdateField(field.id, { label: e.target.value })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id={`required-${field.id}`}
                              checked={field.required}
                              onChange={(e) => handleUpdateField(field.id, { required: e.target.checked })}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <label htmlFor={`required-${field.id}`} className="ml-2 text-xs text-gray-700">
                              Obligatoire
                            </label>
                          </div>
                          <button
                            onClick={() => handleRemoveField(field.id)}
                            className="text-red-400 hover:text-red-600 p-1"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      {(field.type === 'select' || field.type === 'checkbox' || field.type === 'radio') && (
                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-700 mb-1">Options (une par ligne)</label>
                          <textarea
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            rows={3}
                            value={field.options?.join('\n') || ''}
                            onChange={(e) => handleUpdateField(field.id, { 
                              options: e.target.value.split('\n').filter(opt => opt.trim()) 
                            })}
                            placeholder="Option 1&#10;Option 2&#10;Option 3"
                          ></textarea>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    setFormBuilder({ title: '', description: '', targetAudience: 'students', fields: [] });
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={handleSaveForm}
                  disabled={!formBuilder.title || formBuilder.fields.length === 0}
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:opacity-90 disabled:opacity-50"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  Créer le Formulaire
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreviewModal && selectedForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Aperçu du Formulaire</h2>
              <button
                onClick={() => {
                  setShowPreviewModal(false);
                  setSelectedForm(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="text-2xl">&times;</span>
              </button>
            </div>

            <div className="space-y-6">
              <div>
                <h3 className="text-2xl font-bold text-gray-900">{selectedForm.title}</h3>
                <p className="text-gray-600 mt-2">{selectedForm.description}</p>
              </div>

              <form className="space-y-6">
                {selectedForm.fields.map((field) => (
                  <div key={field.id}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {field.label} {field.required && <span className="text-red-500">*</span>}
                    </label>
                    
                    {field.type === 'text' && (
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder={field.placeholder}
                        disabled
                      />
                    )}
                    
                    {field.type === 'textarea' && (
                      <textarea
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={4}
                        placeholder={field.placeholder}
                        disabled
                      ></textarea>
                    )}
                    
                    {field.type === 'select' && (
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" disabled>
                        <option>Sélectionner une option</option>
                        {field.options?.map((option, index) => (
                          <option key={index} value={option}>{option}</option>
                        ))}
                      </select>
                    )}
                    
                    {field.type === 'checkbox' && (
                      <div className="space-y-2">
                        {field.options?.map((option, index) => (
                          <label key={index} className="flex items-center">
                            <input
                              type="checkbox"
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              disabled
                            />
                            <span className="ml-2 text-sm text-gray-700">{option}</span>
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}