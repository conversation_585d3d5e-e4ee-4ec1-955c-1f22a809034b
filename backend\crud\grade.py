# crud/grade.py
from typing import List, Optional
from datetime import date
from sqlalchemy.orm import Session
from sqlalchemy import func
from crud.base import CRUDBase
from models.grade import Grade
from schemas.grade import GradeCreate, GradeUpdate

class CRUDGrade(CRUDBase[Grade, GradeCreate, GradeUpdate]):
    def get_by_student(self, db: Session, *, eleve_id: int) -> List[Grade]:
        return db.query(Grade).filter(Grade.eleve_id == eleve_id).all()
    
    def get_by_course(self, db: Session, *, cours_id: int) -> List[Grade]:
        return db.query(Grade).filter(Grade.cours_id == cours_id).all()
    
    def get_by_student_and_course(
        self, db: Session, *, eleve_id: int, cours_id: int
    ) -> List[Grade]:
        return db.query(Grade).filter(
            Grade.eleve_id == eleve_id,
            Grade.cours_id == cours_id
        ).all()
    
    def get_by_type(self, db: Session, *, type: str) -> List[Grade]:
        return db.query(Grade).filter(Grade.type == type).all()
    
    def get_average_by_student_and_course(
        self, db: Session, *, eleve_id: int, cours_id: int
    ) -> Optional[float]:
        result = db.query(func.avg(Grade.note)).filter(
            Grade.eleve_id == eleve_id,
            Grade.cours_id == cours_id
        ).scalar()
        return float(result) if result else None
    
    def get_by_date_range(
        self, db: Session, *, start_date: date, end_date: date
    ) -> List[Grade]:
        return db.query(Grade).filter(
            Grade.date >= start_date,
            Grade.date <= end_date
        ).all()

grade = CRUDGrade(Grade)
