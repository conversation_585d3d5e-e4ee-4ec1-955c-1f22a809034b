import axios from 'axios';

export interface ApiError {
    message: string;
    status?: number;
    code?: string;
}

export class <PERSON>rrorHandler {
    static handleAuthError(error: unknown): ApiError {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status;
            const detail = error.response?.data?.detail;

            switch (status) {
                case 401:
                    return {
                        message: detail || 'Identifiants incorrects',
                        status: 401,
                        code: 'UNAUTHORIZED'
                    };
                case 400:
                    return {
                        message: detail || 'Données invalides',
                        status: 400,
                        code: 'BAD_REQUEST'
                    };
                case 422:
                    return {
                        message: 'Données de validation incorrectes',
                        status: 422,
                        code: 'VALIDATION_ERROR'
                    };
                case 500:
                    return {
                        message: 'Erreur serveur interne',
                        status: 500,
                        code: 'SERVER_ERROR'
                    };
                default:
                    return {
                        message: detail || 'Une erreur est survenue',
                        status: status || 0,
                        code: 'UNKNOWN_ERROR'
                    };
            }
        }

        return {
            message: 'Erreur de connexion réseau',
            code: 'NETWORK_ERROR'
        };
    }
}
