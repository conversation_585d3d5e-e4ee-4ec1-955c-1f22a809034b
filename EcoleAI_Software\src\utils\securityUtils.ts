import { envConfig } from '../config/environment';

interface LoginAttempt {
  username: string;
  timestamp: number;
  success: boolean;
}

interface UserLockout {
  username: string;
  lockedUntil: number;
  attempts: number;
}

export class SecurityUtils {
  private static readonly LOGIN_ATTEMPTS_KEY = 'ecole_ai_login_attempts';
  private static readonly USER_LOCKOUTS_KEY = 'ecole_ai_user_lockouts';

  static recordLoginAttempt(username: string, success: boolean): void {
    try {
      const attempts = this.getLoginAttempts();
      const newAttempt: LoginAttempt = {
        username: username.toLowerCase(),
        timestamp: Date.now(),
        success
      };

      attempts.push(newAttempt);

      const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
      const recentAttempts = attempts.filter(attempt => attempt.timestamp > oneDayAgo);

      localStorage.setItem(this.LOGIN_ATTEMPTS_KEY, JSON.stringify(recentAttempts));

      if (!success) {
        this.checkAndApplyLockout(username);
      } else {
        this.clearUserLockout(username);
      }
    } catch (error) {
      console.error('Failed to record login attempt:', error);
    }
  }

  static isUserLockedOut(username: string): boolean {
    try {
      const lockouts = this.getUserLockouts();
      const userLockout = lockouts.find(lockout =>
        lockout.username === username.toLowerCase()
      );

      if (!userLockout) return false;

      if (Date.now() > userLockout.lockedUntil) {
        this.clearUserLockout(username);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to check user lockout:', error);
      return false;
    }
  }

  static getRemainingLockoutTime(username: string): number {
    try {
      const lockouts = this.getUserLockouts();
      const userLockout = lockouts.find(lockout =>
        lockout.username === username.toLowerCase()
      );

      if (!userLockout) return 0;

      const remaining = userLockout.lockedUntil - Date.now();
      return Math.max(0, remaining);
    } catch (error) {
      console.error('Failed to get lockout time:', error);
      return 0;
    }
  }

  static getRecentFailedAttempts(username: string): number {
    try {
      const attempts = this.getLoginAttempts();
      const oneHourAgo = Date.now() - (60 * 60 * 1000);

      return attempts.filter(attempt =>
        attempt.username === username.toLowerCase() &&
        !attempt.success &&
        attempt.timestamp > oneHourAgo
      ).length;
    } catch (error) {
      console.error('Failed to get recent failed attempts:', error);
      return 0;
    }
  }

  static validatePasswordStrength(password: string): {
    isValid: boolean;
    errors: string[];
    score: number;
  } {
    const errors: string[] = [];
    let score = 0;

    if (password.length < 8) {
      errors.push('Le mot de passe doit contenir au moins 8 caractères');
    } else {
      score += 1;
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins une lettre majuscule');
    } else {
      score += 1;
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins une lettre minuscule');
    } else {
      score += 1;
    }

    if (!/\d/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins un chiffre');
    } else {
      score += 1;
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins un caractère spécial');
    } else {
      score += 1;
    }

    const commonPasswords = ['password', '123456', 'admin', 'qwerty', 'letmein'];
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Ce mot de passe est trop commun');
      score = Math.max(0, score - 2);
    }

    return {
      isValid: errors.length === 0,
      errors,
      score: Math.min(5, score)
    };
  }

  static sanitizeInput(input: string): string {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  static generateSecureRandomString(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }

  static isSecureConnection(): boolean {
    if (envConfig.isDevelopment()) {
      return true;
    }
    return window.location.protocol === 'https:';
  }

  static clearSecurityData(): void {
    try {
      localStorage.removeItem(this.LOGIN_ATTEMPTS_KEY);
      localStorage.removeItem(this.USER_LOCKOUTS_KEY);
    } catch (error) {
      console.error('Failed to clear security data:', error);
    }
  }

  private static getLoginAttempts(): LoginAttempt[] {
    try {
      const attempts = localStorage.getItem(this.LOGIN_ATTEMPTS_KEY);
      return attempts ? JSON.parse(attempts) : [];
    } catch (error) {
      console.error('Failed to get login attempts:', error);
      return [];
    }
  }

  private static getUserLockouts(): UserLockout[] {
    try {
      const lockouts = localStorage.getItem(this.USER_LOCKOUTS_KEY);
      return lockouts ? JSON.parse(lockouts) : [];
    } catch (error) {
      console.error('Failed to get user lockouts:', error);
      return [];
    }
  }

  private static checkAndApplyLockout(username: string): void {
    const failedAttempts = this.getRecentFailedAttempts(username);

    if (failedAttempts >= envConfig.maxLoginAttempts) {
      const lockouts = this.getUserLockouts();
      const existingLockoutIndex = lockouts.findIndex(lockout =>
        lockout.username === username.toLowerCase()
      );

      const newLockout: UserLockout = {
        username: username.toLowerCase(),
        lockedUntil: Date.now() + envConfig.lockoutDuration,
        attempts: failedAttempts
      };

      if (existingLockoutIndex >= 0) {
        lockouts[existingLockoutIndex] = newLockout;
      } else {
        lockouts.push(newLockout);
      }

      localStorage.setItem(this.USER_LOCKOUTS_KEY, JSON.stringify(lockouts));
    }
  }

  private static clearUserLockout(username: string): void {
    try {
      const lockouts = this.getUserLockouts();
      const filteredLockouts = lockouts.filter(lockout =>
        lockout.username !== username.toLowerCase()
      );
      localStorage.setItem(this.USER_LOCKOUTS_KEY, JSON.stringify(filteredLockouts));
    } catch (error) {
      console.error('Failed to clear user lockout:', error);
    }
  }
}
