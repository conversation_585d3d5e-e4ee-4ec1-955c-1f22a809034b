import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Check<PERSON>ircle,
  ArrowRight,
  ArrowLeft,
  Users,
  DollarSign,
  GraduationCap,
  ClipboardCheck
} from 'lucide-react';
import PrerequisitesCheck from './PrerequisitesCheck';
import StudentRegistrationForm from './StudentRegistrationForm';
import FinancialSetup from './FinancialSetup';
import AcademicAssignment from './AcademicAssignment';
import EnrollmentSummary from './EnrollmentSummary';

interface EnrollmentStep {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  component: React.ComponentType<any>;
  completed: boolean;
  required: boolean;
}

interface StudentData {
  matricule?: string;
  nom?: string;
  prenom?: string;
  date_naissance?: string;
  sexe?: string;
  adresse?: string;
  nom_parent?: string;
  contact_parent?: string;
  classe_id?: number;
  is_active?: boolean;
}

interface EnrollmentData {
  student: StudentData;
  prerequisites: {
    schoolYearActive: boolean;
    classesAvailable: boolean;
    subjectsConfigured: boolean;
    teachersAssigned: boolean;
  };
  financial: {
    registrationFee?: number;
    monthlyFee?: number;
    additionalFees?: Array<{
      type: string;
      amount: number;
      description: string;
    }>;
  };
  academic: {
    selectedClass?: any;
    assignedCourses?: any[];
    assignedTeachers?: any[];
  };
}

function StudentEnrollmentWizard() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [enrollmentData, setEnrollmentData] = useState<EnrollmentData>({
    student: {},
    prerequisites: {
      schoolYearActive: false,
      classesAvailable: false,
      subjectsConfigured: false,
      teachersAssigned: false,
    },
    financial: {},
    academic: {},
  });

  const steps: EnrollmentStep[] = [
    {
      id: 'prerequisites',
      name: 'Vérification des Prérequis',
      description: 'Vérifier que l\'année scolaire, les classes, matières et professeurs sont configurés',
      icon: CheckCircle,
      component: PrerequisitesCheck,
      completed: false,
      required: true,
    },
    {
      id: 'registration',
      name: 'Informations de l\'Élève',
      description: 'Saisir les informations personnelles et académiques de l\'élève',
      icon: Users,
      component: StudentRegistrationForm,
      completed: false,
      required: true,
    },
    {
      id: 'financial',
      name: 'Configuration Financière',
      description: 'Configurer les frais d\'inscription et les paiements récurrents',
      icon: DollarSign,
      component: FinancialSetup,
      completed: false,
      required: true,
    },
    {
      id: 'academic',
      name: 'Affectation Académique',
      description: 'Assigner l\'élève aux cours et vérifier les professeurs',
      icon: GraduationCap,
      component: AcademicAssignment,
      completed: false,
      required: true,
    },
    {
      id: 'summary',
      name: 'Finalisation',
      description: 'Révision finale et confirmation de l\'inscription',
      icon: ClipboardCheck,
      component: EnrollmentSummary,
      completed: false,
      required: true,
    },
  ];

  const [enrollmentSteps, setEnrollmentSteps] = useState(steps);

  const updateStepCompletion = (stepId: string, completed: boolean) => {
    setEnrollmentSteps(prev =>
      prev.map(step =>
        step.id === stepId ? { ...step, completed } : step
      )
    );
  };

  const updateEnrollmentData = (section: keyof EnrollmentData, data: any) => {
    setEnrollmentData(prev => ({
      ...prev,
      [section]: { ...prev[section], ...data }
    }));
  };

  const canProceedToNext = () => {
    const currentStepData = enrollmentSteps[currentStep];
    return currentStepData.completed || !currentStepData.required;
  };

  const handleNext = () => {
    if (currentStep < enrollmentSteps.length - 1 && canProceedToNext()) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    if (stepIndex <= currentStep) {
      setCurrentStep(stepIndex);
    }
  };

  const CurrentStepComponent = enrollmentSteps[currentStep].component;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Inscription d'un Nouvel Élève</h1>
          <p className="mt-2 text-gray-600">
            Suivez les étapes pour inscrire un nouvel élève dans le système
          </p>
        </div>

        <div className="mb-8">
          <nav aria-label="Progress">
            {/* Desktop Stepper */}
            <div className="hidden lg:block">
              <div className="relative">
                {/* Connector Line Background */}
                <div className="absolute top-6 left-0 right-0 h-1 bg-gray-200 rounded-full" style={{ left: '3rem', right: '3rem' }}>
                  <div
                    className="h-full bg-green-500 rounded-full transition-all duration-700 ease-in-out"
                    style={{
                      width: `${(currentStep / (enrollmentSteps.length - 1)) * 100}%`,
                      boxShadow: currentStep > 0 ? '0 0 8px rgba(34, 197, 94, 0.3)' : 'none'
                    }}
                  />
                </div>

                {/* Steps */}
                <ol className="flex justify-between items-start relative z-10">
                  {enrollmentSteps.map((step, stepIndex) => {
                    const isCurrent = stepIndex === currentStep;
                    const isCompleted = step.completed;
                    const isClickable = stepIndex <= currentStep;

                    return (
                      <li key={step.id} className="flex flex-col items-center">
                        <button
                          onClick={() => handleStepClick(stepIndex)}
                          disabled={!isClickable}
                          className={`relative flex h-12 w-12 items-center justify-center rounded-full border-2 transition-all duration-200 bg-white ${isCompleted
                            ? 'border-green-600 bg-green-600 text-white shadow-lg'
                            : isCurrent
                              ? 'text-white shadow-lg'
                              : isClickable
                                ? 'border-gray-300 bg-white text-gray-500 hover:border-gray-400 hover:shadow-md'
                                : 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                            }`}
                          style={isCurrent ? { backgroundColor: '#0a1186', borderColor: '#0a1186' } : {}}
                        >
                          {isCompleted ? (
                            <CheckCircle className="h-6 w-6" />
                          ) : (
                            React.createElement(step.icon, { className: "h-6 w-6" })
                          )}
                        </button>
                        <div className="mt-4 text-center max-w-36">
                          <p className={`text-sm font-medium leading-tight ${isCurrent ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                            }`} style={isCurrent ? { color: '#0a1186' } : {}}>
                            {step.name}
                          </p>
                          <p className="text-xs text-gray-500 mt-1 leading-tight">{step.description}</p>
                        </div>
                      </li>
                    );
                  })}
                </ol>
              </div>
            </div>

            {/* Mobile/Tablet Stepper */}
            <div className="lg:hidden">
              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm font-medium text-gray-500">
                    Étape {currentStep + 1} sur {enrollmentSteps.length}
                  </span>
                  <span className="text-sm text-gray-500">
                    {Math.round(((currentStep + 1) / enrollmentSteps.length) * 100)}% terminé
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                  <div
                    className="h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${((currentStep + 1) / enrollmentSteps.length) * 100}%`,
                      backgroundColor: '#0a1186'
                    }}
                  />
                </div>
                <div className="flex items-center">
                  <div
                    className="flex h-10 w-10 items-center justify-center rounded-full text-white"
                    style={{ backgroundColor: '#0a1186' }}
                  >
                    {React.createElement(enrollmentSteps[currentStep].icon, { className: "h-5 w-5" })}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">
                      {enrollmentSteps[currentStep].name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {enrollmentSteps[currentStep].description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </nav>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <CurrentStepComponent
            enrollmentData={enrollmentData}
            updateEnrollmentData={updateEnrollmentData}
            updateStepCompletion={updateStepCompletion}
            currentStep={enrollmentSteps[currentStep]}
          />
        </div>

        <div className="flex justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${currentStep === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Précédent
          </button>

          <div className="flex space-x-3">
            <button
              onClick={() => navigate('/students/directory')}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Annuler
            </button>

            {currentStep < enrollmentSteps.length - 1 ? (
              <button
                onClick={handleNext}
                disabled={!canProceedToNext()}
                className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${canProceedToNext()
                  ? 'text-white hover:opacity-90 shadow-md'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                style={canProceedToNext() ? { backgroundColor: '#0a1186' } : {}}
              >
                Suivant
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            ) : (
              <button
                onClick={() => {
                  console.log('Final enrollment data:', enrollmentData);
                  navigate('/students/directory');
                }}
                disabled={!canProceedToNext()}
                className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${canProceedToNext()
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Finaliser l'Inscription
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default StudentEnrollmentWizard;
