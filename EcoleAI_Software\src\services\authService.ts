import { apiClient, API_ENDPOINTS } from '../config/api';

export interface LoginCredentials {
    username: string;
    password: string;
}

export interface RegisterData {
    username: string;
    email: string;
    password: string;
    full_name: string;
    phone?: string;
    role?: string;
}

export interface AuthResponse {
    access_token: string;
    refresh_token: string;
    token_type: string;
}

export interface User {
    id: number;
    username: string;
    email: string;
    full_name: string;
    phone?: string;
    role?: string;
    disabled?: boolean;
    created_at?: string;
}

export class AuthService {
    static async login(credentials: LoginCredentials): Promise<AuthResponse> {
        const formData = new FormData();
        formData.append('username', credentials.username);
        formData.append('password', credentials.password);

        const response = await apiClient.post(API_ENDPOINTS.LOGIN, formData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        });

        return response.data;
    }

    static async register(userData: RegisterData): Promise<User> {
        const response = await apiClient.post(API_ENDPOINTS.REGISTER, userData);
        return response.data;
    }

    static async getCurrentUser(): Promise<User> {
        const response = await apiClient.get(API_ENDPOINTS.USER_PROFILE);
        return response.data;
    }

    static async refreshToken(refreshToken: string): Promise<AuthResponse> {
        const response = await apiClient.post(API_ENDPOINTS.REFRESH, {
            refresh_token: refreshToken
        });
        return response.data;
    }

    static logout(): void {
        // Use SecureTokenStorage instead of direct localStorage
        import('../utils/tokenStorage').then(({ SecureTokenStorage }) => {
            SecureTokenStorage.clearTokens();
        }).catch(error => {
            console.error('Failed to clear tokens:', error);
            // Fallback to direct localStorage clearing
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('ecole-ai-user');
        });
    }
}