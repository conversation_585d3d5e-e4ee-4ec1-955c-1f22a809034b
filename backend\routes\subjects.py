# routes/subjects.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from utils.security import get_current_active_user, get_db
from crud.subject import subject
from schemas.subject import SubjectCreate, SubjectRead, SubjectUpdate
from services.academic_service import AcademicService

router = APIRouter(prefix="/api/subjects", tags=["Subjects"])

@router.get("/", response_model=List[SubjectRead])
async def get_subjects(
    skip: int = 0,
    limit: int = 100,
    search: str = Query(None, description="Search subjects by name"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all subjects with optional search"""
    if search:
        return subject.search_by_name(db, nom=search)
    return subject.get_multi(db, skip=skip, limit=limit)

@router.get("/{subject_id}", response_model=SubjectRead)
async def get_subject(
    subject_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific subject by ID"""
    db_subject = subject.get(db, subject_id)
    if not db_subject:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subject not found"
        )
    return db_subject

@router.get("/{subject_id}/performance")
async def get_subject_performance(
    subject_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get performance statistics for a subject"""
    performance_data = AcademicService.get_subject_performance(db, subject_id)
    if "error" in performance_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=performance_data["error"]
        )
    return performance_data

@router.post("/", response_model=SubjectRead)
async def create_subject(
    subject_in: SubjectCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new subject"""
    # Check if subject already exists
    existing = subject.get_by_name(db, nom=subject_in.nom)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Subject with this name already exists"
        )
    
    return subject.create(db, obj_in=subject_in)

@router.put("/{subject_id}", response_model=SubjectRead)
async def update_subject(
    subject_id: int,
    subject_in: SubjectUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a subject"""
    db_subject = subject.get(db, subject_id)
    if not db_subject:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subject not found"
        )
    
    return subject.update(db, db_obj=db_subject, obj_in=subject_in)

@router.delete("/{subject_id}")
async def delete_subject(
    subject_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a subject"""
    db_subject = subject.get(db, subject_id)
    if not db_subject:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subject not found"
        )
    
    subject.remove(db, id=subject_id)
    return {"message": "Subject deleted successfully"}
