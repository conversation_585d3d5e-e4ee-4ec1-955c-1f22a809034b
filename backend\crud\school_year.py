# crud/school_year.py
from typing import Optional
from sqlalchemy.orm import Session
from crud.base import CRUDBase
from models.school_year import SchoolYear
from schemas.school_year import SchoolYearCreate, SchoolYearUpdate

class CRUDSchoolYear(CRUDBase[SchoolYear, SchoolYearCreate, SchoolYearUpdate]):
    def get_by_annee(self, db: Session, *, annee: str) -> Optional[SchoolYear]:
        return db.query(SchoolYear).filter(SchoolYear.annee == annee).first()
    
    def get_active(self, db: Session) -> Optional[SchoolYear]:
        return db.query(SchoolYear).filter(SchoolYear.is_active == True).first()
    
    def set_active(self, db: Session, *, school_year_id: int) -> SchoolYear:
        # First, deactivate all school years
        db.query(SchoolYear).update({SchoolYear.is_active: False})
        
        # Then activate the specified one
        school_year = self.get(db, school_year_id)
        if school_year:
            school_year.is_active = True
            db.add(school_year)
            db.commit()
            db.refresh(school_year)
        return school_year

school_year = CRUDSchoolYear(SchoolYear)
