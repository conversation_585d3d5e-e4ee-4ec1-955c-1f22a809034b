import { runAllAuthTests } from '../utils/authTest';

export default function AuthTester() {
  const handleRunTests = async () => {
    await runAllAuthTests();
  };

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={handleRunTests}
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow-lg text-sm font-medium"
      >
        🧪 Run Auth Tests
      </button>
    </div>
  );
}
