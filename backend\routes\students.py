# routes/students.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from utils.security import get_current_active_user, get_db
from crud.student import student
from schemas.student import StudentCreate, StudentRead, StudentUpdate
from services.student_service import StudentService
from services.financial_service import FinancialService

router = APIRouter(prefix="/api/students", tags=["Students"])

@router.get("/", response_model=List[StudentRead])
async def get_students(
    skip: int = 0,
    limit: int = 100,
    class_id: int = Query(None, description="Filter by class"),
    active_only: bool = Query(True, description="Get only active students"),
    search: str = Query(None, description="Search students by name"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all students with optional filters"""
    if search:
        return student.search_by_name(db, name=search)
    elif class_id:
        return student.get_by_class(db, classe_id=class_id)
    elif active_only:
        return student.get_active(db)
    else:
        return student.get_multi(db, skip=skip, limit=limit)

@router.get("/generate-matricule")
async def generate_student_matricule(
    year: int = Query(None, description="Year for matricule generation"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Generate a new unique student matricule"""
    matricule = StudentService.generate_matricule(db, year)
    return {"matricule": matricule}

@router.get("/{student_id}", response_model=StudentRead)
async def get_student(
    student_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific student by ID"""
    db_student = student.get(db, student_id)
    if not db_student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    return db_student

@router.get("/{student_id}/academic-summary")
async def get_student_academic_summary(
    student_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get comprehensive academic summary for a student"""
    summary = StudentService.get_student_academic_summary(db, student_id)
    if not summary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    return summary

@router.get("/{student_id}/financial-status")
async def get_student_financial_status(
    student_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get detailed financial status for a student"""
    financial_status = FinancialService.get_student_financial_status(db, student_id)
    if "error" in financial_status:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=financial_status["error"]
        )
    return financial_status

@router.post("/", response_model=StudentRead)
async def create_student(
    student_in: StudentCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new student"""
    # Check if matricule already exists
    existing = student.get_by_matricule(db, matricule=student_in.matricule)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Student with this matricule already exists"
        )
    
    return student.create(db, obj_in=student_in)

@router.put("/{student_id}", response_model=StudentRead)
async def update_student(
    student_id: int,
    student_in: StudentUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a student"""
    db_student = student.get(db, student_id)
    if not db_student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Check matricule uniqueness if updating matricule
    if student_in.matricule and student_in.matricule != db_student.matricule:
        existing = student.get_by_matricule(db, matricule=student_in.matricule)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Student with this matricule already exists"
            )
    
    return student.update(db, db_obj=db_student, obj_in=student_in)

@router.delete("/{student_id}")
async def delete_student(
    student_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a student (soft delete by setting is_active to False)"""
    db_student = student.get(db, student_id)
    if not db_student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Soft delete by setting is_active to False
    student.update(db, db_obj=db_student, obj_in={"is_active": False})
    return {"message": "Student deactivated successfully"}
