import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { SecureTokenStorage } from '../utils/tokenStorage';
import { envConfig } from '../config/environment';

export function useTokenExpiration() {
    const { logout } = useAuth();

    useEffect(() => {
        const checkTokenExpiration = () => {
            const token = SecureTokenStorage.getAccessToken();
            if (token) {
                try {
                    if (SecureTokenStorage.isTokenExpired(token)) {
                        console.log('Token expired, logging out...');
                        logout();
                        return;
                    }

                    const expiration = SecureTokenStorage.getTokenExpiration(token);
                    if (expiration) {
                        const timeUntilExpiry = expiration.getTime() - Date.now();
                        if (timeUntilExpiry < envConfig.tokenRefreshThreshold) {
                            console.log('Token close to expiry, will be refreshed by interceptor');
                        }
                    }
                } catch (error) {
                    console.error('Error checking token expiration:', error);
                    logout();
                }
            }
        };

        checkTokenExpiration();

        const interval = setInterval(checkTokenExpiration, 5 * 60 * 1000);

        return () => clearInterval(interval);
    }, [logout]);
}