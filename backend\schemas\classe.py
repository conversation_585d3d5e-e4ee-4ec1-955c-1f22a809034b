# schemas/classe.py
from pydantic import BaseModel, validator
from datetime import datetime
from typing import Optional

class ClasseBase(BaseModel):
    nom: str
    niveau: str
    annee_scolaire_id: int

    @validator('nom')
    def validate_nom(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Class name must be at least 2 characters long')
        return v.strip()

    @validator('niveau')
    def validate_niveau(cls, v):
        valid_niveaux = [
            'CP', 'CE1', 'CE2', 'CM1', 'CM2',  # Primary
            '6ème', '5ème', '4ème', '3ème',     # Middle school
            'Seconde', 'Première', 'Terminale'  # High school
        ]
        if v not in valid_niveaux:
            raise ValueError(f'Level must be one of: {", ".join(valid_niveaux)}')
        return v

class ClasseCreate(ClasseBase):
    pass

class ClasseUpdate(BaseModel):
    nom: Optional[str] = None
    niveau: Optional[str] = None
    annee_scolaire_id: Optional[int] = None

class ClasseRead(ClasseBase):
    id: int
    effectif: int  # Calculated field
    created_at: datetime

    class Config:
        from_attributes = True
