import React, { useState, useEffect } from 'react';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Calendar,
  Building,
  BookOpen,
  UserCheck,
  Plus
} from 'lucide-react';
import { apiClient } from '../../../config/api';
import SchoolYearModal from './modals/SchoolYearModal';
import ClassModal from './modals/ClassModal';
import SubjectModal from './modals/SubjectModal';
import TeacherModal from './modals/TeacherModal';

interface PrerequisitesCheckProps {
  enrollmentData: any;
  updateEnrollmentData: (section: string, data: any) => void;
  updateStepCompletion: (stepId: string, completed: boolean) => void;
  currentStep: any;
}

interface PrerequisiteItem {
  id: string;
  name: string;
  description: string;
  status: 'checking' | 'success' | 'warning' | 'error';
  icon: React.ComponentType<any>;
  data?: any;
  actionRequired?: boolean;
  actionText?: string;
}

export default function PrerequisitesCheck({
  enrollmentData,
  updateEnrollmentData,
  updateStepCompletion,
  currentStep
}: PrerequisitesCheckProps) {
  const [isChecking, setIsChecking] = useState(false);
  const [prerequisites, setPrerequisites] = useState<PrerequisiteItem[]>([
    {
      id: 'school-year',
      name: 'Année Scolaire Active',
      description: 'Une année scolaire doit être active pour permettre les inscriptions',
      status: 'checking',
      icon: Calendar,
      actionRequired: false,
    },
    {
      id: 'classes',
      name: 'Classes Disponibles',
      description: 'Des classes doivent être configurées pour l\'année scolaire active',
      status: 'checking',
      icon: Building,
      actionRequired: false,
    },
    {
      id: 'subjects',
      name: 'Matières Configurées',
      description: 'Les matières doivent être définies dans le système',
      status: 'checking',
      icon: BookOpen,
      actionRequired: false,
    },
    {
      id: 'teachers',
      name: 'Professeurs Actifs',
      description: 'Des professeurs actifs doivent être disponibles',
      status: 'checking',
      icon: UserCheck,
      actionRequired: false,
    },
  ]);

  // Modal states
  const [showSchoolYearModal, setShowSchoolYearModal] = useState(false);
  const [showClassModal, setShowClassModal] = useState(false);
  const [showSubjectModal, setShowSubjectModal] = useState(false);
  const [showTeacherModal, setShowTeacherModal] = useState(false);

  useEffect(() => {
    checkPrerequisites();
  }, []);

  const checkPrerequisites = async () => {
    setIsChecking(true);

    try {
      // Check School Years
      const schoolYearsResponse = await apiClient.get('/api/school-years/');
      const schoolYears = schoolYearsResponse.data;
      const activeSchoolYear = schoolYears.find((year: any) => year.is_active);

      // Check Classes
      const classesResponse = await apiClient.get('/api/classes/');
      const classes = classesResponse.data;
      const currentYearClasses = activeSchoolYear
        ? classes.filter((cls: any) => cls.annee_scolaire_id === activeSchoolYear.id)
        : [];

      // Check Subjects
      const subjectsResponse = await apiClient.get('/api/subjects/');
      const subjects = subjectsResponse.data;

      // Check Teachers
      const teachersResponse = await apiClient.get('/api/teachers/?active_only=true');
      const teachers = teachersResponse.data;

      // Update prerequisites status
      setPrerequisites(prev => prev.map(prereq => {
        switch (prereq.id) {
          case 'school-year':
            return {
              ...prereq,
              status: activeSchoolYear ? 'success' : 'error',
              data: activeSchoolYear,
              actionRequired: !activeSchoolYear,
              actionText: !activeSchoolYear ? 'Créer une année scolaire' : undefined,
            };
          case 'classes':
            return {
              ...prereq,
              status: currentYearClasses.length > 0 ? 'success' : 'warning',
              data: currentYearClasses,
              actionRequired: currentYearClasses.length === 0,
              actionText: currentYearClasses.length === 0 ? 'Créer des classes' : undefined,
            };
          case 'subjects':
            return {
              ...prereq,
              status: subjects.length > 0 ? 'success' : 'warning',
              data: subjects,
              actionRequired: subjects.length === 0,
              actionText: subjects.length === 0 ? 'Ajouter des matières' : undefined,
            };
          case 'teachers':
            return {
              ...prereq,
              status: teachers.length > 0 ? 'success' : 'warning',
              data: teachers,
              actionRequired: teachers.length === 0,
              actionText: teachers.length === 0 ? 'Ajouter des professeurs' : undefined,
            };
          default:
            return prereq;
        }
      }));

      // Update enrollment data
      updateEnrollmentData('prerequisites', {
        schoolYearActive: !!activeSchoolYear,
        classesAvailable: currentYearClasses.length > 0,
        subjectsConfigured: subjects.length > 0,
        teachersAssigned: teachers.length > 0,
        activeSchoolYear,
        availableClasses: currentYearClasses,
        subjects,
        teachers,
      });

      // Check if step is completed (all critical prerequisites met)
      const allCriticalMet = !!activeSchoolYear && currentYearClasses.length > 0;
      updateStepCompletion(currentStep.id, allCriticalMet);

    } catch (error) {
      console.error('Error checking prerequisites:', error);
      setPrerequisites(prev => prev.map(prereq => ({
        ...prereq,
        status: 'error' as const
      })));
    } finally {
      setIsChecking(false);
    }
  };

  // Modal success handlers
  const handleSchoolYearCreated = (schoolYear: any) => {
    console.log('School year created:', schoolYear);
    // Refresh prerequisites to update the data
    checkPrerequisites();
  };

  const handleClassCreated = (classe: any) => {
    console.log('Class created:', classe);
    // Refresh prerequisites to update the data
    checkPrerequisites();
  };

  const handleSubjectCreated = (subject: any) => {
    console.log('Subject created:', subject);
    // Refresh prerequisites to update the data
    checkPrerequisites();
  };

  const handleTeacherCreated = (teacher: any) => {
    console.log('Teacher created:', teacher);
    // Refresh prerequisites to update the data
    checkPrerequisites();
  };

  // Handle action button clicks
  const handleActionClick = (prereqId: string) => {
    switch (prereqId) {
      case 'school-year':
        setShowSchoolYearModal(true);
        break;
      case 'classes':
        setShowClassModal(true);
        break;
      case 'subjects':
        setShowSubjectModal(true);
        break;
      case 'teachers':
        setShowTeacherModal(true);
        break;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'checking':
      default:
        return <RefreshCw className="h-5 w-5 text-gray-400 animate-spin" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'checking':
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const criticalIssues = prerequisites.filter(p => p.status === 'error').length;
  const warnings = prerequisites.filter(p => p.status === 'warning').length;
  const allGood = prerequisites.filter(p => p.status === 'success').length === prerequisites.length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Vérification des Prérequis</h2>
          <p className="text-sm text-gray-600 mt-1">
            Vérification de la configuration système nécessaire pour l'inscription d'élèves
          </p>
        </div>
        <button
          onClick={checkPrerequisites}
          disabled={isChecking}
          className="flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
          Revérifier
        </button>
      </div>

      {/* Overall Status */}
      <div className={`p-4 rounded-lg border ${criticalIssues > 0
        ? 'border-red-200 bg-red-50'
        : warnings > 0
          ? 'border-yellow-200 bg-yellow-50'
          : allGood
            ? 'border-green-200 bg-green-50'
            : 'border-gray-200 bg-gray-50'
        }`}>
        <div className="flex items-center">
          {criticalIssues > 0 ? (
            <XCircle className="h-5 w-5 text-red-500 mr-3" />
          ) : warnings > 0 ? (
            <AlertTriangle className="h-5 w-5 text-yellow-500 mr-3" />
          ) : allGood ? (
            <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
          ) : (
            <RefreshCw className="h-5 w-5 text-gray-400 mr-3 animate-spin" />
          )}
          <div>
            <h3 className="font-medium">
              {criticalIssues > 0
                ? 'Configuration Incomplète'
                : warnings > 0
                  ? 'Configuration Partielle'
                  : allGood
                    ? 'Configuration Complète'
                    : 'Vérification en cours...'
              }
            </h3>
            <p className="text-sm text-gray-600">
              {criticalIssues > 0
                ? `${criticalIssues} problème(s) critique(s) à résoudre avant de continuer`
                : warnings > 0
                  ? `${warnings} avertissement(s) - vous pouvez continuer mais certaines fonctionnalités peuvent être limitées`
                  : allGood
                    ? 'Tous les prérequis sont satisfaits'
                    : 'Vérification des prérequis système...'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Prerequisites List */}
      <div className="space-y-4">
        {prerequisites.map((prereq) => (
          <div
            key={prereq.id}
            className={`p-4 rounded-lg border transition-colors ${getStatusColor(prereq.status)}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  <prereq.icon className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-900">{prereq.name}</h4>
                    {getStatusIcon(prereq.status)}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{prereq.description}</p>

                  {/* Show data summary */}
                  {prereq.data && prereq.status === 'success' && (
                    <div className="mt-2 text-sm text-gray-700">
                      {prereq.id === 'school-year' && (
                        <span>Année active: {prereq.data.annee}</span>
                      )}
                      {prereq.id === 'classes' && (
                        <span>{prereq.data.length} classe(s) disponible(s)</span>
                      )}
                      {prereq.id === 'subjects' && (
                        <span>{prereq.data.length} matière(s) configurée(s)</span>
                      )}
                      {prereq.id === 'teachers' && (
                        <span>{prereq.data.length} professeur(s) actif(s)</span>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Action Button */}
              {prereq.actionRequired && prereq.actionText && (
                <button
                  onClick={() => handleActionClick(prereq.id)}
                  className="flex items-center px-3 py-1 text-sm font-medium text-white rounded-md hover:opacity-90 transition-colors"
                  style={{ backgroundColor: '#0a1186' }}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  {prereq.actionText}
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Next Steps */}
      {allGood && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-blue-500 mr-3" />
            <div>
              <h4 className="font-medium text-blue-900">Prêt pour l'inscription</h4>
              <p className="text-sm text-blue-700">
                Tous les prérequis sont satisfaits. Vous pouvez procéder à l'inscription d'un nouvel élève.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      <SchoolYearModal
        isOpen={showSchoolYearModal}
        onClose={() => setShowSchoolYearModal(false)}
        onSuccess={handleSchoolYearCreated}
      />

      <ClassModal
        isOpen={showClassModal}
        onClose={() => setShowClassModal(false)}
        onSuccess={handleClassCreated}
        schoolYears={enrollmentData.prerequisites?.activeSchoolYear ? [enrollmentData.prerequisites.activeSchoolYear] : []}
      />

      <SubjectModal
        isOpen={showSubjectModal}
        onClose={() => setShowSubjectModal(false)}
        onSuccess={handleSubjectCreated}
      />

      <TeacherModal
        isOpen={showTeacherModal}
        onClose={() => setShowTeacherModal(false)}
        onSuccess={handleTeacherCreated}
      />
    </div>
  );
}
