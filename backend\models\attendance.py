# models/attendance.py
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Date
from sqlalchemy.orm import relationship
from datetime import datetime, date
from db import Base

class Attendance(Base):
    __tablename__ = "attendances"

    id = Column(Integer, primary_key=True, index=True)
    eleve_id = Column(Integer, ForeignKey("students.id"), nullable=False)
    cours_id = Column(Integer, ForeignKey("courses.id"), nullable=False)
    date = Column(Date, nullable=False, default=date.today)
    statut = Column(String, nullable=False)  # present, absent, late
    commentaire = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    student = relationship("Student", back_populates="attendances")
    course = relationship("Course", back_populates="attendances")
