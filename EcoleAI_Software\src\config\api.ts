import axios from "axios";
import { envConfig } from './environment';
import { SecureTokenStorage } from '../utils/tokenStorage';

export const API_BASE_URL = envConfig.apiBaseUrl;
export const API_ENDPOINTS = {
    LOGIN: '/auth/token',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    USER_PROFILE: '/users/me',
};

export const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

apiClient.interceptors.request.use(
    (config) => {
        const token = SecureTokenStorage.getAccessToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
                const refreshToken = SecureTokenStorage.getRefreshToken();
                if (refreshToken) {
                    const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
                        refresh_token: refreshToken
                    });

                    const { access_token, refresh_token: newRefreshToken } = response.data;

                    SecureTokenStorage.setTokens(access_token, newRefreshToken || refreshToken);

                    originalRequest.headers.Authorization = `Bearer ${access_token}`;
                    return apiClient(originalRequest);
                }
            } catch (refreshError) {
                console.error('Refresh failed:', refreshError);
                SecureTokenStorage.clearTokens();
                if (window.location.pathname !== '/login') {
                    window.location.href = '/login';
                }
            }
        }

        return Promise.reject(error);
    }
);