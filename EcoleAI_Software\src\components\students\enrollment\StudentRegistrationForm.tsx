import React, { useState, useEffect, useCallback } from 'react';
import {
  User,
  Calendar,
  MapPin,
  Phone,
  Users,
  Hash,
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { apiClient } from '../../../config/api';

interface StudentRegistrationFormProps {
  enrollmentData: any;
  updateEnrollmentData: (section: string, data: any) => void;
  updateStepCompletion: (stepId: string, completed: boolean) => void;
  currentStep: any;
}

interface FormData {
  matricule: string;
  nom: string;
  prenom: string;
  date_naissance: string;
  sexe: string;
  adresse: string;
  nom_parent: string;
  contact_parent: string;
  classe_id: string;
  is_active: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export default function StudentRegistrationForm({
  enrollmentData,
  updateEnrollmentData,
  updateStepCompletion,
  currentStep
}: StudentRegistrationFormProps) {
  const [formData, setFormData] = useState<FormData>({
    matricule: '',
    nom: '',
    prenom: '',
    date_naissance: '',
    sexe: '',
    adresse: '',
    nom_parent: '',
    contact_parent: '',
    classe_id: '',
    is_active: true,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isGeneratingMatricule, setIsGeneratingMatricule] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Get available classes from enrollment data (loaded by PrerequisitesCheck)
  const availableClasses = enrollmentData.prerequisites?.availableClasses || [];

  useEffect(() => {
    // Load existing data if available
    if (enrollmentData.student) {
      setFormData(prev => ({ ...prev, ...enrollmentData.student }));
    }

    // Generate matricule if not already set
    if (!formData.matricule) {
      generateMatricule();
    }
  }, [enrollmentData]);

  // Define validateForm function first
  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors = {};

    // Required fields validation
    if (!formData.matricule.trim()) {
      newErrors.matricule = 'Le matricule est requis';
    } else if (formData.matricule.length < 5) {
      newErrors.matricule = 'Le matricule doit contenir au moins 5 caractères';
    }

    if (!formData.nom.trim()) {
      newErrors.nom = 'Le nom est requis';
    } else if (formData.nom.length < 2) {
      newErrors.nom = 'Le nom doit contenir au moins 2 caractères';
    }

    if (!formData.prenom.trim()) {
      newErrors.prenom = 'Le prénom est requis';
    } else if (formData.prenom.length < 2) {
      newErrors.prenom = 'Le prénom doit contenir au moins 2 caractères';
    }

    if (!formData.date_naissance) {
      newErrors.date_naissance = 'La date de naissance est requise';
    } else {
      // Validate age (between 3 and 25 years)
      const birthDate = new Date(formData.date_naissance);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear() -
        ((today.getMonth() < birthDate.getMonth() ||
          (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())) ? 1 : 0);

      if (age < 3 || age > 25) {
        newErrors.date_naissance = 'L\'âge doit être entre 3 et 25 ans';
      }
    }

    if (!formData.sexe) {
      newErrors.sexe = 'Le sexe est requis';
    }

    if (!formData.nom_parent.trim()) {
      newErrors.nom_parent = 'Le nom du parent/tuteur est requis';
    } else if (formData.nom_parent.length < 2) {
      newErrors.nom_parent = 'Le nom du parent doit contenir au moins 2 caractères';
    }

    if (!formData.contact_parent.trim()) {
      newErrors.contact_parent = 'Le contact du parent est requis';
    } else if (formData.contact_parent.length < 8) {
      newErrors.contact_parent = 'Le contact doit contenir au moins 8 caractères';
    }

    if (!formData.classe_id) {
      newErrors.classe_id = 'La classe est requise';
    }

    // Only update errors if they have actually changed
    const errorsChanged = JSON.stringify(newErrors) !== JSON.stringify(errors);
    if (errorsChanged) {
      setErrors(newErrors);
    }

    return Object.keys(newErrors).length === 0;
  }, [formData, errors]);

  useEffect(() => {
    // Debounce validation and data update to prevent glitching
    const timeoutId = setTimeout(() => {
      const isValid = validateForm();
      updateStepCompletion(currentStep.id, isValid);

      // Update enrollment data
      if (isValid) {
        updateEnrollmentData('student', formData);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [formData, validateForm, currentStep.id, updateStepCompletion, updateEnrollmentData]);

  const generateMatricule = async () => {
    setIsGeneratingMatricule(true);
    try {
      const response = await apiClient.get('/api/students/generate-matricule');
      setFormData(prev => ({ ...prev, matricule: response.data.matricule }));
    } catch (error) {
      console.error('Error generating matricule:', error);
      setErrors(prev => ({ ...prev, matricule: 'Erreur lors de la génération du matricule' }));
    } finally {
      setIsGeneratingMatricule(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const checkMatriculeUniqueness = async () => {
    if (!formData.matricule || formData.matricule.length < 5) return;

    setIsValidating(true);
    try {
      const response = await apiClient.get(`/api/students/?search=${formData.matricule}`);
      const students = response.data;
      const exists = students.some((student: any) => student.matricule === formData.matricule);

      if (exists) {
        setErrors(prev => ({ ...prev, matricule: 'Ce matricule existe déjà' }));
      }
    } catch (error) {
      console.error('Error checking matricule uniqueness:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const getSelectedClass = () => {
    return availableClasses.find(cls => cls.id.toString() === formData.classe_id);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Informations de l'Élève</h2>
        <p className="text-sm text-gray-600 mt-1">
          Saisissez les informations personnelles et académiques de l'élève
        </p>
      </div>

      <form className="space-y-6">
        {/* Student ID */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Hash className="inline h-4 w-4 mr-1" />
            Matricule de l'Élève
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={formData.matricule}
              onChange={(e) => handleInputChange('matricule', e.target.value.toUpperCase())}
              onBlur={checkMatriculeUniqueness}
              className={`flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.matricule ? 'border-red-300' : 'border-gray-300'
                }`}
              placeholder="EAI2024001"
            />
            <button
              type="button"
              onClick={generateMatricule}
              disabled={isGeneratingMatricule}
              className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 transition-colors"
            >
              {isGeneratingMatricule ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                'Générer'
              )}
            </button>
          </div>
          {errors.matricule && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              {errors.matricule}
            </p>
          )}
          {isValidating && (
            <p className="mt-1 text-sm text-blue-600 flex items-center">
              <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
              Vérification de l'unicité...
            </p>
          )}
        </div>

        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="inline h-4 w-4 mr-1" />
              Nom de Famille *
            </label>
            <input
              type="text"
              value={formData.nom}
              onChange={(e) => handleInputChange('nom', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.nom ? 'border-red-300' : 'border-gray-300'
                }`}
              placeholder="Dupont"
            />
            {errors.nom && (
              <p className="mt-1 text-sm text-red-600">{errors.nom}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="inline h-4 w-4 mr-1" />
              Prénom *
            </label>
            <input
              type="text"
              value={formData.prenom}
              onChange={(e) => handleInputChange('prenom', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.prenom ? 'border-red-300' : 'border-gray-300'
                }`}
              placeholder="Jean"
            />
            {errors.prenom && (
              <p className="mt-1 text-sm text-red-600">{errors.prenom}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              Date de Naissance *
            </label>
            <input
              type="date"
              value={formData.date_naissance}
              onChange={(e) => handleInputChange('date_naissance', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.date_naissance ? 'border-red-300' : 'border-gray-300'
                }`}
            />
            {errors.date_naissance && (
              <p className="mt-1 text-sm text-red-600">{errors.date_naissance}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Users className="inline h-4 w-4 mr-1" />
              Sexe *
            </label>
            <select
              value={formData.sexe}
              onChange={(e) => handleInputChange('sexe', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.sexe ? 'border-red-300' : 'border-gray-300'
                }`}
            >
              <option value="">Sélectionner</option>
              <option value="M">Masculin</option>
              <option value="F">Féminin</option>
            </select>
            {errors.sexe && (
              <p className="mt-1 text-sm text-red-600">{errors.sexe}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="inline h-4 w-4 mr-1" />
            Adresse
          </label>
          <textarea
            value={formData.adresse}
            onChange={(e) => handleInputChange('adresse', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Adresse complète de l'élève"
          />
        </div>

        {/* Parent Information */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Informations du Parent/Tuteur</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="inline h-4 w-4 mr-1" />
                Nom du Parent/Tuteur *
              </label>
              <input
                type="text"
                value={formData.nom_parent}
                onChange={(e) => handleInputChange('nom_parent', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.nom_parent ? 'border-red-300' : 'border-gray-300'
                  }`}
                placeholder="Marie Dupont"
              />
              {errors.nom_parent && (
                <p className="mt-1 text-sm text-red-600">{errors.nom_parent}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Phone className="inline h-4 w-4 mr-1" />
                Contact du Parent *
              </label>
              <input
                type="text"
                value={formData.contact_parent}
                onChange={(e) => handleInputChange('contact_parent', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.contact_parent ? 'border-red-300' : 'border-gray-300'
                  }`}
                placeholder="+261 32 23 456 79"
              />
              {errors.contact_parent && (
                <p className="mt-1 text-sm text-red-600">{errors.contact_parent}</p>
              )}
            </div>
          </div>
        </div>

        {/* Class Assignment */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Affectation de Classe</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Classe *
            </label>
            <select
              value={formData.classe_id}
              onChange={(e) => handleInputChange('classe_id', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.classe_id ? 'border-red-300' : 'border-gray-300'
                }`}
            >
              <option value="">Sélectionner une classe</option>
              {availableClasses.map((classe) => (
                <option key={classe.id} value={classe.id}>
                  {classe.nom} - {classe.niveau}
                </option>
              ))}
            </select>
            {errors.classe_id && (
              <p className="mt-1 text-sm text-red-600">{errors.classe_id}</p>
            )}

            {getSelectedClass() && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <h4 className="font-medium text-blue-900">Classe Sélectionnée</h4>
                <p className="text-sm text-blue-700">
                  {getSelectedClass().nom} - Niveau: {getSelectedClass().niveau}
                </p>
                <p className="text-sm text-blue-700">
                  Année scolaire: {enrollmentData.prerequisites?.activeSchoolYear?.annee || 'Non définie'}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Form Status */}
        {Object.keys(errors).length === 0 && formData.nom && formData.prenom && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
              <div>
                <h4 className="font-medium text-green-900">Formulaire Valide</h4>
                <p className="text-sm text-green-700">
                  Toutes les informations requises ont été saisies correctement.
                </p>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
